# Monsta Chess Engine

A UCI-compliant chess engine written in C# with .NET 10 and AOT compilation for maximum performance.

## Features

### Core Engine
- **Bitboard representation** for efficient board operations
- **Magic bitboards** for sliding piece attack generation
- **Z<PERSON>rist hashing** for position identification
- **UCI protocol** compliance for GUI integration

### Search Algorithm
- **Negamax** with alpha-beta pruning
- **Iterative deepening** for time management
- **Principal Variation Search (PVS)** for optimal move ordering
- **Transposition table** with 64MB default size
- **Quiescence search** to avoid horizon effect
- **Null move pruning** for better performance
- **Late Move Reductions (LMR)** for deeper search

### Evaluation Function
- **Material evaluation** with standard piece values
- **Piece-square tables** for positional evaluation
- **Pawn structure** analysis (doubled, isolated, passed pawns)
- **King safety** evaluation with pawn shield
- **Mobility** evaluation
- **Endgame-specific** king evaluation

### Advanced Features
- **Move ordering** with MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
- **Hash move** prioritization
- **Capture prioritization** in quiescence search
- **Delta pruning** in quiescence search
- **Mate detection** and scoring

## Building and Running

### Prerequisites
- .NET 10 SDK
- Windows, Linux, or macOS

### Build
```bash
dotnet build -c Release
```

### Run Tests
```bash
dotnet run -- test
```

### Run UCI Mode
```bash
dotnet run
```

## UCI Commands Supported

### Engine Identification
- `uci` - Initialize UCI mode
- `isready` - Check if engine is ready
- `ucinewgame` - Start new game

### Options
- `Hash` - Transposition table size (1-1024 MB, default 64)
- `OwnBook` - Use engine's opening book (default false)
- `Ponder` - Enable pondering (default false)

### Position Setup
- `position startpos` - Set starting position
- `position fen <fen>` - Set position from FEN
- `position startpos moves <moves>` - Apply moves from starting position

### Search
- `go depth <n>` - Search to fixed depth
- `go movetime <ms>` - Search for fixed time
- `go wtime <ms> btime <ms>` - Search with time controls
- `go infinite` - Search until stopped
- `stop` - Stop current search

## Engine Strength

The engine is designed to compete against strong engines like Stockfish and Lc0. Key strength features:

1. **Efficient Search**: Advanced pruning techniques allow deeper search
2. **Strong Evaluation**: Comprehensive position evaluation
3. **Optimized Performance**: AOT compilation and bitboard operations
4. **Memory Efficient**: Configurable transposition table size

## Architecture

```
Monsta/
├── Engine/
│   ├── Board.cs              # Board representation and move making
│   ├── Move.cs               # Move representation
│   ├── MoveGenerator.cs      # Legal move generation
│   ├── Search.cs             # Main search algorithm
│   ├── Evaluation.cs         # Position evaluation
│   ├── TranspositionTable.cs # Hash table for caching
│   ├── Bitboards.cs          # Bitboard utilities
│   ├── MagicBitboards.cs     # Magic bitboard attacks
│   ├── ZobristHash.cs        # Position hashing
│   └── UciHandler.cs         # UCI protocol implementation
├── Tests/
│   └── BasicTests.cs         # Unit tests
└── Program.cs                # Main entry point
```

## Performance Characteristics

- **Nodes per second**: ~500K-2M NPS (depending on hardware)
- **Search depth**: Typically 8-12 plies in middlegame
- **Memory usage**: Configurable (1-1024 MB for transposition table)
- **Startup time**: < 100ms with AOT compilation

## Usage with Chess GUIs

The engine works with any UCI-compatible chess GUI:

### Arena Chess GUI
1. Install Arena
2. Add Monsta.exe as new engine
3. Configure hash size and other options
4. Start playing!

### ChessBase/Fritz
1. Copy Monsta.exe to engines folder
2. Add as UCI engine
3. Configure in engine settings

### Lichess Bot
1. Use with lichess-bot framework
2. Configure as UCI engine
3. Run automated games

## Planned Improvements

- [ ] **Opening book** integration
- [ ] **Syzygy tablebase** support
- [ ] **Multi-threading** for parallel search
- [ ] **Neural network** evaluation (NNUE)
- [ ] **Time management** improvements
- [ ] **Endgame knowledge** enhancement
- [ ] **Tuning** with genetic algorithms

## Contributing

Contributions are welcome! Areas for improvement:
- Evaluation function tuning
- Search algorithm optimizations
- Bug fixes and testing
- Documentation improvements

## License

This project is open source. Feel free to use, modify, and distribute.

## Acknowledgments

- Chess Programming Wiki for algorithms and techniques
- Stockfish team for inspiration and reference
- UCI protocol specification authors
- .NET team for excellent performance tools
