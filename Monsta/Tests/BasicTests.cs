using Monsta.Engine;

namespace Monsta.Tests;

/// <summary>
/// Basic tests for the chess engine
/// </summary>
public static class BasicTests
{
    public static void RunAllTests()
    {
        Console.WriteLine("Running basic tests...");
        
        TestBoardSetup();
        TestMoveGeneration();
        TestMakeUnmakeMove();
        TestEvaluation();
        TestSearch();
        
        Console.WriteLine("All tests completed!");
    }

    private static void TestBoardSetup()
    {
        Console.WriteLine("Testing board setup...");
        
        var board = new Board();
        
        // Test starting position
        string expectedFen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
        string actualFen = board.ToFen();
        
        if (actualFen == expectedFen)
        {
            Console.WriteLine("✓ Board setup test passed");
        }
        else
        {
            Console.WriteLine($"✗ Board setup test failed");
            Console.WriteLine($"Expected: {expectedFen}");
            Console.WriteLine($"Actual:   {actualFen}");
        }
    }

    private static void TestMoveGeneration()
    {
        Console.WriteLine("Testing move generation...");
        
        var board = new Board();
        var moves = MoveGenerator.GenerateMoves(board);
        
        // Starting position should have 20 legal moves
        if (moves.Count == 20)
        {
            Console.WriteLine("✓ Move generation test passed");
        }
        else
        {
            Console.WriteLine($"✗ Move generation test failed - expected 20 moves, got {moves.Count}");
        }
    }

    private static void TestMakeUnmakeMove()
    {
        Console.WriteLine("Testing make/unmake move...");
        
        var board = new Board();
        string originalFen = board.ToFen();
        
        // Make a move (e2-e4)
        var move = new Move(12, 28); // e2 to e4
        var gameState = board.MakeMove(move);
        
        // Unmake the move
        board.UnmakeMove(move, gameState);
        
        string restoredFen = board.ToFen();
        
        if (originalFen == restoredFen)
        {
            Console.WriteLine("✓ Make/unmake move test passed");
        }
        else
        {
            Console.WriteLine($"✗ Make/unmake move test failed");
            Console.WriteLine($"Original:  {originalFen}");
            Console.WriteLine($"Restored:  {restoredFen}");
        }
    }

    private static void TestEvaluation()
    {
        Console.WriteLine("Testing evaluation...");
        
        var board = new Board();
        int score = Evaluation.Evaluate(board);
        
        // Starting position should be roughly equal (close to 0)
        if (Math.Abs(score) < 100)
        {
            Console.WriteLine($"✓ Evaluation test passed (score: {score})");
        }
        else
        {
            Console.WriteLine($"✗ Evaluation test failed - score too high: {score}");
        }
    }

    private static void TestSearch()
    {
        Console.WriteLine("Testing search...");
        
        var board = new Board();
        var search = new Search(1); // 1MB hash
        
        try
        {
            var result = search.StartSearch(board, depth: 3);
            
            if (!result.BestMove.IsNull && result.NodesSearched > 0)
            {
                Console.WriteLine($"✓ Search test passed");
                Console.WriteLine($"  Best move: {result.BestMove}");
                Console.WriteLine($"  Score: {result.Score}");
                Console.WriteLine($"  Nodes: {result.NodesSearched}");
            }
            else
            {
                Console.WriteLine("✗ Search test failed - no move found");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"✗ Search test failed with exception: {ex.Message}");
        }
    }
}
