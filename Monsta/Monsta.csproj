<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net10.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PublishAot>true</PublishAot>
    <InvariantGlobalization>true</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.ML" Version="3.0.1" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.17.1" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime.Managed" Version="1.17.1" />
    <PackageReference Include="System.Numerics.Tensors" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="zekrom-v6.nnue">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
