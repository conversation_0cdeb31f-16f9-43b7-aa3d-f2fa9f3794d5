namespace Monsta.Engine;

/// <summary>
/// Zobrist hashing for position identification in transposition table
/// </summary>
public static class ZobristHash
{
    private static readonly ulong[,] PieceKeys = new ulong[12, 64]; // 12 piece types, 64 squares
    private static readonly ulong[] CastlingKeys = new ulong[16]; // 16 possible castling states
    private static readonly ulong[] EnPassantKeys = new ulong[8]; // 8 files for en passant
    private static readonly ulong BlackToMoveKey;

    static ZobristHash()
    {
        var random = new Random(12345); // Fixed seed for reproducible hashes
        
        // Initialize piece keys
        for (int piece = 0; piece < 12; piece++)
        {
            for (int square = 0; square < 64; square++)
            {
                PieceKeys[piece, square] = NextULong(random);
            }
        }
        
        // Initialize castling keys
        for (int i = 0; i < 16; i++)
        {
            CastlingKeys[i] = NextULong(random);
        }
        
        // Initialize en passant keys
        for (int file = 0; file < 8; file++)
        {
            EnPassantKeys[file] = NextULong(random);
        }
        
        BlackToMoveKey = NextULong(random);
    }

    public static ulong ComputeHash(Board board)
    {
        ulong hash = 0;

        // Hash pieces
        for (int square = 0; square < 64; square++)
        {
            var (piece, color) = board.GetPiece(square);
            if (piece != PieceType.None)
            {
                int pieceIndex = GetPieceIndex(piece, color);
                hash ^= PieceKeys[pieceIndex, square];
            }
        }

        // Hash castling rights
        hash ^= CastlingKeys[board.CastlingRights];

        // Hash en passant
        if (board.EnPassantSquare != -1)
        {
            int file = Bitboards.GetFile(board.EnPassantSquare);
            hash ^= EnPassantKeys[file];
        }

        // Hash side to move
        if (board.SideToMove == Color.Black)
        {
            hash ^= BlackToMoveKey;
        }

        return hash;
    }

    public static ulong UpdateHashAfterMove(ulong currentHash, Move move, Board board)
    {
        ulong hash = currentHash;

        // Toggle side to move
        hash ^= BlackToMoveKey;

        // Remove piece from source square
        var (movingPiece, movingColor) = board.GetPiece(move.From);
        if (movingPiece != PieceType.None)
        {
            int pieceIndex = GetPieceIndex(movingPiece, movingColor);
            hash ^= PieceKeys[pieceIndex, move.From];
        }

        // Add piece to destination square
        PieceType finalPiece = move.IsPromotion ? move.Promotion : movingPiece;
        if (finalPiece != PieceType.None)
        {
            int pieceIndex = GetPieceIndex(finalPiece, movingColor);
            hash ^= PieceKeys[pieceIndex, move.To];
        }

        // Handle captures
        if (move.IsCapture && !move.IsEnPassant)
        {
            var (capturedPiece, capturedColor) = board.GetPiece(move.To);
            if (capturedPiece != PieceType.None)
            {
                int pieceIndex = GetPieceIndex(capturedPiece, capturedColor);
                hash ^= PieceKeys[pieceIndex, move.To];
            }
        }

        // Handle en passant capture
        if (move.IsEnPassant)
        {
            Color opponentColor = movingColor == Color.White ? Color.Black : Color.White;
            int capturedPawnSquare = movingColor == Color.White ? move.To - 8 : move.To + 8;
            int pieceIndex = GetPieceIndex(PieceType.Pawn, opponentColor);
            hash ^= PieceKeys[pieceIndex, capturedPawnSquare];
        }

        // Handle castling
        if (move.IsCastle)
        {
            // Remove and add rook for castling
            int rookFrom, rookTo;
            if (move.To > move.From) // King side
            {
                rookFrom = movingColor == Color.White ? 7 : 63;
                rookTo = movingColor == Color.White ? 5 : 61;
            }
            else // Queen side
            {
                rookFrom = movingColor == Color.White ? 0 : 56;
                rookTo = movingColor == Color.White ? 3 : 59;
            }

            int rookIndex = GetPieceIndex(PieceType.Rook, movingColor);
            hash ^= PieceKeys[rookIndex, rookFrom];
            hash ^= PieceKeys[rookIndex, rookTo];
        }

        return hash;
    }

    public static ulong UpdateHashForCastlingRights(ulong hash, int oldRights, int newRights)
    {
        hash ^= CastlingKeys[oldRights];
        hash ^= CastlingKeys[newRights];
        return hash;
    }

    public static ulong UpdateHashForEnPassant(ulong hash, int oldSquare, int newSquare)
    {
        if (oldSquare != -1)
        {
            int oldFile = Bitboards.GetFile(oldSquare);
            hash ^= EnPassantKeys[oldFile];
        }

        if (newSquare != -1)
        {
            int newFile = Bitboards.GetFile(newSquare);
            hash ^= EnPassantKeys[newFile];
        }

        return hash;
    }

    private static int GetPieceIndex(PieceType piece, Color color)
    {
        return (int)piece - 1 + (int)color * 6;
    }

    private static ulong NextULong(Random random)
    {
        byte[] bytes = new byte[8];
        random.NextBytes(bytes);
        return BitConverter.ToUInt64(bytes, 0);
    }
}
