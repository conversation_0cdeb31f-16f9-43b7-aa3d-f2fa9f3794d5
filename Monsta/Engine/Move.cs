namespace Monsta.Engine;

/// <summary>
/// Represents a chess move with efficient bit-packed storage
/// </summary>
public readonly struct Move : IEquatable<Move>
{
    private readonly uint _data;

    // Bit layout: 
    // 0-5: from square (6 bits)
    // 6-11: to square (6 bits) 
    // 12-15: move type (4 bits)
    // 16-18: promotion piece (3 bits)
    // 19-21: captured piece (3 bits)
    // 22-31: unused (10 bits)

    private const uint FromMask = 0x3F;
    private const uint ToMask = 0x3F << 6;
    private const uint TypeMask = 0xF << 12;
    private const uint PromotionMask = 0x7 << 16;
    private const uint CapturedMask = 0x7 << 19;

    public Move(int from, int to, MoveType type = MoveType.Normal, PieceType promotion = PieceType.None, PieceType captured = PieceType.None)
    {
        _data = (uint)from |
                ((uint)to << 6) |
                ((uint)type << 12) |
                ((uint)promotion << 16) |
                ((uint)captured << 19);
    }

    public int From => (int)(_data & FromMask);
    public int To => (int)((_data & ToMask) >> 6);
    public MoveType Type => (MoveType)((_data & TypeMask) >> 12);
    public PieceType Promotion => (PieceType)((_data & PromotionMask) >> 16);
    public PieceType Captured => (PieceType)((_data & CapturedMask) >> 19);

    public bool IsCapture => Captured != PieceType.None || Type == MoveType.EnPassant;
    public bool IsPromotion => Type == MoveType.Promotion;
    public bool IsCastle => Type == MoveType.Castle;
    public bool IsEnPassant => Type == MoveType.EnPassant;

    public static readonly Move NullMove = new(0, 0);

    public bool IsNull => _data == 0;

    public string ToUciString()
    {
        if (IsNull) return "0000";

        var from = SquareToString(From);
        var to = SquareToString(To);
        var promotion = IsPromotion ? PieceTypeToChar(Promotion).ToString().ToLower() : "";
        
        return from + to + promotion;
    }

    public static Move FromUciString(string uci)
    {
        if (uci == "0000") return NullMove;
        if (uci.Length < 4) throw new ArgumentException("Invalid UCI move string");

        var from = StringToSquare(uci[..2]);
        var to = StringToSquare(uci[2..4]);
        var promotion = uci.Length > 4 ? CharToPieceType(uci[4]) : PieceType.None;

        return new Move(from, to, promotion != PieceType.None ? MoveType.Promotion : MoveType.Normal, promotion);
    }

    private static string SquareToString(int square)
    {
        var file = (char)('a' + (square & 7));
        var rank = (char)('1' + (square >> 3));
        return $"{file}{rank}";
    }

    private static int StringToSquare(string square)
    {
        var file = square[0] - 'a';
        var rank = square[1] - '1';
        return rank * 8 + file;
    }

    private static char PieceTypeToChar(PieceType piece) => piece switch
    {
        PieceType.Queen => 'Q',
        PieceType.Rook => 'R',
        PieceType.Bishop => 'B',
        PieceType.Knight => 'N',
        _ => ' '
    };

    private static PieceType CharToPieceType(char c) => char.ToUpper(c) switch
    {
        'Q' => PieceType.Queen,
        'R' => PieceType.Rook,
        'B' => PieceType.Bishop,
        'N' => PieceType.Knight,
        _ => PieceType.None
    };

    public override string ToString() => ToUciString();

    public bool Equals(Move other) => _data == other._data;
    public override bool Equals(object? obj) => obj is Move move && Equals(move);
    public override int GetHashCode() => _data.GetHashCode();

    public static bool operator ==(Move left, Move right) => left.Equals(right);
    public static bool operator !=(Move left, Move right) => !left.Equals(right);
}

public enum MoveType : byte
{
    Normal = 0,
    Castle = 1,
    EnPassant = 2,
    Promotion = 3
}

public enum PieceType : byte
{
    None = 0,
    Pawn = 1,
    Knight = 2,
    Bishop = 3,
    Rook = 4,
    Queen = 5,
    King = 6
}

public enum Color : byte
{
    White = 0,
    Black = 1
}
