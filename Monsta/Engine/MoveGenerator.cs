namespace Monsta.Engine;

/// <summary>
/// Generates legal chess moves for a given position
/// </summary>
public static class MoveGenerator
{
    public static List<Move> GenerateMoves(Board board, bool capturesOnly = false)
    {
        var moves = new List<Move>();
        Color sideToMove = board.SideToMove;
        ulong friendlyPieces = board.GetColorBitboard(sideToMove);
        ulong enemyPieces = board.GetColorBitboard(sideToMove == Color.White ? Color.Black : Color.White);
        ulong allPieces = board.GetAllPieces();
        ulong emptySquares = ~allPieces;

        // Generate pawn moves
        GeneratePawnMoves(board, moves, sideToMove, friendlyPieces, enemyPieces, emptySquares, capturesOnly);

        // Generate piece moves
        GeneratePieceMoves(board, moves, PieceType.Knight, sideToMove, friendlyPieces, enemyPieces, capturesOnly);
        GeneratePieceMoves(board, moves, PieceType.Bishop, sideToMove, friendlyPieces, enemyPieces, capturesOnly);
        GeneratePieceMoves(board, moves, PieceType.Rook, sideToMove, friendlyPieces, enemyPieces, capturesOnly);
        GeneratePieceMoves(board, moves, PieceType.Queen, sideToMove, friendlyPieces, enemyPieces, capturesOnly);
        GeneratePieceMoves(board, moves, PieceType.King, sideToMove, friendlyPieces, enemyPieces, capturesOnly);

        // Generate castling moves (only if not captures only)
        if (!capturesOnly)
        {
            GenerateCastlingMoves(board, moves, sideToMove, allPieces);
        }

        // Filter out illegal moves (moves that leave king in check)
        return FilterLegalMoves(board, moves);
    }

    private static void GeneratePawnMoves(Board board, List<Move> moves, Color color, ulong friendlyPieces, ulong enemyPieces, ulong emptySquares, bool capturesOnly)
    {
        ulong pawns = board.GetPieceBitboard(PieceType.Pawn, color);
        int direction = color == Color.White ? 8 : -8;
        int startRank = color == Color.White ? 1 : 6;
        int promotionRank = color == Color.White ? 7 : 0;

        while (pawns != 0)
        {
            int from = Bitboards.PopLsb(ref pawns);
            int rank = Bitboards.GetRank(from);
            int file = Bitboards.GetFile(from);

            // Pawn pushes (only if not captures only)
            if (!capturesOnly)
            {
                int oneSquareForward = from + direction;
                if (oneSquareForward >= 0 && oneSquareForward < 64 && (emptySquares & (1UL << oneSquareForward)) != 0)
                {
                    if (rank == promotionRank - direction / 8)
                    {
                        // Promotion
                        moves.Add(new Move(from, oneSquareForward, MoveType.Promotion, PieceType.Queen));
                        moves.Add(new Move(from, oneSquareForward, MoveType.Promotion, PieceType.Rook));
                        moves.Add(new Move(from, oneSquareForward, MoveType.Promotion, PieceType.Bishop));
                        moves.Add(new Move(from, oneSquareForward, MoveType.Promotion, PieceType.Knight));
                    }
                    else
                    {
                        moves.Add(new Move(from, oneSquareForward));

                        // Double pawn push
                        if (rank == startRank)
                        {
                            int twoSquaresForward = from + 2 * direction;
                            if (twoSquaresForward >= 0 && twoSquaresForward < 64 && (emptySquares & (1UL << twoSquaresForward)) != 0)
                            {
                                moves.Add(new Move(from, twoSquaresForward));
                            }
                        }
                    }
                }
            }

            // Pawn captures
            ulong pawnAttacks = color == Color.White ? Bitboards.WhitePawnAttacks[from] : Bitboards.BlackPawnAttacks[from];
            ulong captures = pawnAttacks & enemyPieces;

            while (captures != 0)
            {
                int to = Bitboards.PopLsb(ref captures);
                var (capturedPiece, _) = board.GetPiece(to);

                if (rank == promotionRank - direction / 8)
                {
                    // Capture promotion
                    moves.Add(new Move(from, to, MoveType.Promotion, PieceType.Queen, capturedPiece));
                    moves.Add(new Move(from, to, MoveType.Promotion, PieceType.Rook, capturedPiece));
                    moves.Add(new Move(from, to, MoveType.Promotion, PieceType.Bishop, capturedPiece));
                    moves.Add(new Move(from, to, MoveType.Promotion, PieceType.Knight, capturedPiece));
                }
                else
                {
                    moves.Add(new Move(from, to, MoveType.Normal, PieceType.None, capturedPiece));
                }
            }

            // En passant capture
            if (board.EnPassantSquare != -1)
            {
                int epSquare = board.EnPassantSquare;
                if ((pawnAttacks & (1UL << epSquare)) != 0)
                {
                    moves.Add(new Move(from, epSquare, MoveType.EnPassant, PieceType.None, PieceType.Pawn));
                }
            }
        }
    }

    private static void GeneratePieceMoves(Board board, List<Move> moves, PieceType pieceType, Color color, ulong friendlyPieces, ulong enemyPieces, bool capturesOnly)
    {
        ulong pieces = board.GetPieceBitboard(pieceType, color);
        ulong allPieces = board.GetAllPieces();

        while (pieces != 0)
        {
            int from = Bitboards.PopLsb(ref pieces);
            ulong attacks = GetPieceAttacks(pieceType, from, allPieces);
            
            // Remove friendly pieces from attacks
            attacks &= ~friendlyPieces;

            if (capturesOnly)
            {
                attacks &= enemyPieces;
            }

            while (attacks != 0)
            {
                int to = Bitboards.PopLsb(ref attacks);
                var (capturedPiece, _) = board.GetPiece(to);
                
                MoveType moveType = pieceType == PieceType.King && Math.Abs(to - from) == 2 ? MoveType.Castle : MoveType.Normal;
                moves.Add(new Move(from, to, moveType, PieceType.None, capturedPiece));
            }
        }
    }

    private static void GenerateCastlingMoves(Board board, List<Move> moves, Color color, ulong allPieces)
    {
        if (board.IsInCheck(color)) return;

        int kingSquare = color == Color.White ? 4 : 60;
        int kingSideRook = color == Color.White ? 7 : 63;
        int queenSideRook = color == Color.White ? 0 : 56;

        // King side castling
        int kingSideCastlingRight = color == Color.White ? Board.WhiteKingSide : Board.BlackKingSide;
        if ((board.CastlingRights & kingSideCastlingRight) != 0)
        {
            if ((allPieces & (1UL << (kingSquare + 1))) == 0 && 
                (allPieces & (1UL << (kingSquare + 2))) == 0)
            {
                if (!board.IsSquareAttacked(kingSquare + 1, color == Color.White ? Color.Black : Color.White) &&
                    !board.IsSquareAttacked(kingSquare + 2, color == Color.White ? Color.Black : Color.White))
                {
                    moves.Add(new Move(kingSquare, kingSquare + 2, MoveType.Castle));
                }
            }
        }

        // Queen side castling
        int queenSideCastlingRight = color == Color.White ? Board.WhiteQueenSide : Board.BlackQueenSide;
        if ((board.CastlingRights & queenSideCastlingRight) != 0)
        {
            if ((allPieces & (1UL << (kingSquare - 1))) == 0 && 
                (allPieces & (1UL << (kingSquare - 2))) == 0 &&
                (allPieces & (1UL << (kingSquare - 3))) == 0)
            {
                if (!board.IsSquareAttacked(kingSquare - 1, color == Color.White ? Color.Black : Color.White) &&
                    !board.IsSquareAttacked(kingSquare - 2, color == Color.White ? Color.Black : Color.White))
                {
                    moves.Add(new Move(kingSquare, kingSquare - 2, MoveType.Castle));
                }
            }
        }
    }

    private static ulong GetPieceAttacks(PieceType pieceType, int square, ulong allPieces)
    {
        return pieceType switch
        {
            PieceType.Knight => Bitboards.KnightAttacks[square],
            PieceType.Bishop => MagicBitboards.GetBishopAttacks(square, allPieces),
            PieceType.Rook => MagicBitboards.GetRookAttacks(square, allPieces),
            PieceType.Queen => MagicBitboards.GetQueenAttacks(square, allPieces),
            PieceType.King => Bitboards.KingAttacks[square],
            _ => 0UL
        };
    }

    private static List<Move> FilterLegalMoves(Board board, List<Move> moves)
    {
        var legalMoves = new List<Move>();
        
        foreach (var move in moves)
        {
            if (IsLegalMove(board, move))
            {
                legalMoves.Add(move);
            }
        }
        
        return legalMoves;
    }

    private static bool IsLegalMove(Board board, Move move)
    {
        // Make the move temporarily
        var originalState = board.ToFen();
        MakeMove(board, move);
        
        // Check if the king is in check after the move
        Color originalSideToMove = board.SideToMove == Color.White ? Color.Black : Color.White;
        bool isLegal = !board.IsInCheck(originalSideToMove);
        
        // Restore the original position
        board.SetFromFen(originalState);
        
        return isLegal;
    }

    // Simplified move making for legality checking
    private static void MakeMove(Board board, Move move)
    {
        // This is a simplified version - in a real engine, this would be more efficient
        var (movingPiece, movingColor) = board.GetPiece(move.From);
        
        // Remove piece from source
        board.RemovePiece(move.From);
        
        // Handle captures
        if (move.IsCapture && !move.IsEnPassant)
        {
            board.RemovePiece(move.To);
        }
        
        // Handle en passant
        if (move.IsEnPassant)
        {
            int capturedPawnSquare = movingColor == Color.White ? move.To - 8 : move.To + 8;
            board.RemovePiece(capturedPawnSquare);
        }
        
        // Place piece on destination
        PieceType finalPiece = move.IsPromotion ? move.Promotion : movingPiece;
        board.SetPiece(move.To, finalPiece, movingColor);
        
        // Handle castling
        if (move.IsCastle)
        {
            int rookFrom, rookTo;
            if (move.To > move.From) // King side
            {
                rookFrom = movingColor == Color.White ? 7 : 63;
                rookTo = movingColor == Color.White ? 5 : 61;
            }
            else // Queen side
            {
                rookFrom = movingColor == Color.White ? 0 : 56;
                rookTo = movingColor == Color.White ? 3 : 59;
            }
            
            board.RemovePiece(rookFrom);
            board.SetPiece(rookTo, PieceType.Rook, movingColor);
        }
    }
}
