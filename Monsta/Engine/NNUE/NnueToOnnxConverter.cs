using System.Text;
using System.Numerics.Tensors;

namespace Monsta.Engine.NNUE;

/// <summary>
/// Converts NNUE networks to ONNX format for use with Microsoft.ML
/// </summary>
public class NnueToOnnxConverter
{
    public static void ConvertToOnnx(NnueParser.NnueNetwork network, string outputPath)
    {
        var onnxModel = CreateOnnxModel(network);
        SaveOnnxModel(onnxModel, outputPath);
    }

    private static OnnxModel CreateOnnxModel(NnueParser.NnueNetwork network)
    {
        var model = new OnnxModel();
        
        // Add input node (768 features)
        model.AddInput("input", new[] { 1, 768 }, OnnxDataType.Float);
        
        // Add feature transformer layer (768 -> 1536 with king buckets)
        var ft = network.FeatureTransformer;
        model.AddLinearLayer("feature_transformer", "input", "ft_output", 
                           ft.Weights, ft.Biases, 768, 1536);
        
        // Add ClippedReLU activation (0, 1)
        model.AddClippedReLU("ft_relu", "ft_output", "ft_activated", 0.0f, 1.0f);
        
        // Concatenate both perspectives (1536 * 2 = 3072)
        model.AddConcat("concat", new[] { "ft_activated", "ft_activated" }, "concat_output", axis: 1);
        
        // Add hidden layers
        string currentOutput = "concat_output";
        for (int i = 0; i < network.HiddenLayers.Length; i++)
        {
            var layer = network.HiddenLayers[i];
            string layerName = $"hidden_{i}";
            string layerOutput = $"hidden_{i}_output";
            string activatedOutput = $"hidden_{i}_activated";
            
            model.AddLinearLayer(layerName, currentOutput, layerOutput,
                               layer.Weights, layer.Biases, layer.InputSize, layer.OutputSize);
            
            model.AddClippedReLU($"{layerName}_relu", layerOutput, activatedOutput, 0.0f, 1.0f);
            currentOutput = activatedOutput;
        }
        
        // Add output layer with 8 buckets
        var outputLayer = network.OutputLayer;
        model.AddLinearLayer("output", currentOutput, "raw_output",
                           outputLayer.Weights, outputLayer.Biases, 
                           outputLayer.InputSize, outputLayer.OutputSize);
        
        // Add final activation (typically no activation for regression output)
        model.AddOutput("raw_output", new[] { 1, 8 }, OnnxDataType.Float);
        
        return model;
    }

    private static void SaveOnnxModel(OnnxModel model, string outputPath)
    {
        // Create a simplified ONNX protobuf representation
        var onnxBytes = model.SerializeToBytes();
        File.WriteAllBytes(outputPath, onnxBytes);
    }
}

/// <summary>
/// Simplified ONNX model representation for our specific use case
/// </summary>
public class OnnxModel
{
    private readonly List<OnnxNode> _nodes = new();
    private readonly List<OnnxTensor> _initializers = new();
    private readonly List<OnnxValueInfo> _inputs = new();
    private readonly List<OnnxValueInfo> _outputs = new();

    public void AddInput(string name, int[] shape, OnnxDataType dataType)
    {
        _inputs.Add(new OnnxValueInfo { Name = name, Shape = shape, DataType = dataType });
    }

    public void AddOutput(string name, int[] shape, OnnxDataType dataType)
    {
        _outputs.Add(new OnnxValueInfo { Name = name, Shape = shape, DataType = dataType });
    }

    public void AddLinearLayer(string name, string input, string output, 
                              float[] weights, float[] biases, int inputSize, int outputSize)
    {
        // Add weight tensor
        var weightName = $"{name}_weight";
        _initializers.Add(new OnnxTensor
        {
            Name = weightName,
            Shape = new[] { outputSize, inputSize },
            Data = weights,
            DataType = OnnxDataType.Float
        });

        // Add bias tensor
        var biasName = $"{name}_bias";
        _initializers.Add(new OnnxTensor
        {
            Name = biasName,
            Shape = new[] { outputSize },
            Data = biases,
            DataType = OnnxDataType.Float
        });

        // Add MatMul node
        _nodes.Add(new OnnxNode
        {
            OpType = "MatMul",
            Name = $"{name}_matmul",
            Inputs = new[] { input, weightName },
            Outputs = new[] { $"{name}_matmul_output" }
        });

        // Add Add node for bias
        _nodes.Add(new OnnxNode
        {
            OpType = "Add",
            Name = $"{name}_add",
            Inputs = new[] { $"{name}_matmul_output", biasName },
            Outputs = new[] { output }
        });
    }

    public void AddClippedReLU(string name, string input, string output, float min, float max)
    {
        _nodes.Add(new OnnxNode
        {
            OpType = "Clip",
            Name = name,
            Inputs = new[] { input },
            Outputs = new[] { output },
            Attributes = new Dictionary<string, object>
            {
                ["min"] = min,
                ["max"] = max
            }
        });
    }

    public void AddConcat(string name, string[] inputs, string output, int axis)
    {
        _nodes.Add(new OnnxNode
        {
            OpType = "Concat",
            Name = name,
            Inputs = inputs,
            Outputs = new[] { output },
            Attributes = new Dictionary<string, object>
            {
                ["axis"] = axis
            }
        });
    }

    public byte[] SerializeToBytes()
    {
        // This is a simplified serialization - in a real implementation,
        // you would use the ONNX protobuf format
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        // Write a simplified binary format that can be loaded by our custom loader
        writer.Write("ONNX".ToCharArray());
        writer.Write(1); // Version

        // Write inputs
        writer.Write(_inputs.Count);
        foreach (var input in _inputs)
        {
            WriteValueInfo(writer, input);
        }

        // Write outputs
        writer.Write(_outputs.Count);
        foreach (var output in _outputs)
        {
            WriteValueInfo(writer, output);
        }

        // Write initializers
        writer.Write(_initializers.Count);
        foreach (var tensor in _initializers)
        {
            WriteTensor(writer, tensor);
        }

        // Write nodes
        writer.Write(_nodes.Count);
        foreach (var node in _nodes)
        {
            WriteNode(writer, node);
        }

        return stream.ToArray();
    }

    private static void WriteValueInfo(BinaryWriter writer, OnnxValueInfo valueInfo)
    {
        writer.Write(valueInfo.Name);
        writer.Write(valueInfo.Shape.Length);
        foreach (var dim in valueInfo.Shape)
        {
            writer.Write(dim);
        }
        writer.Write((int)valueInfo.DataType);
    }

    private static void WriteTensor(BinaryWriter writer, OnnxTensor tensor)
    {
        writer.Write(tensor.Name);
        writer.Write(tensor.Shape.Length);
        foreach (var dim in tensor.Shape)
        {
            writer.Write(dim);
        }
        writer.Write((int)tensor.DataType);
        writer.Write(tensor.Data.Length);
        foreach (var value in tensor.Data)
        {
            writer.Write(value);
        }
    }

    private static void WriteNode(BinaryWriter writer, OnnxNode node)
    {
        writer.Write(node.OpType);
        writer.Write(node.Name);
        writer.Write(node.Inputs.Length);
        foreach (var input in node.Inputs)
        {
            writer.Write(input);
        }
        writer.Write(node.Outputs.Length);
        foreach (var output in node.Outputs)
        {
            writer.Write(output);
        }
        writer.Write(node.Attributes?.Count ?? 0);
        if (node.Attributes != null)
        {
            foreach (var attr in node.Attributes)
            {
                writer.Write(attr.Key);
                writer.Write(attr.Value.ToString() ?? "");
            }
        }
    }
}

public class OnnxValueInfo
{
    public required string Name { get; set; }
    public required int[] Shape { get; set; }
    public required OnnxDataType DataType { get; set; }
}

public class OnnxTensor
{
    public required string Name { get; set; }
    public required int[] Shape { get; set; }
    public required float[] Data { get; set; }
    public required OnnxDataType DataType { get; set; }
}

public class OnnxNode
{
    public required string OpType { get; set; }
    public required string Name { get; set; }
    public required string[] Inputs { get; set; }
    public required string[] Outputs { get; set; }
    public Dictionary<string, object>? Attributes { get; set; }
}

public enum OnnxDataType
{
    Float = 1,
    Int32 = 6,
    Int64 = 7
}
