using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;
using System.Collections.Concurrent;

namespace Monsta.Engine.NNUE;

/// <summary>
/// Neural Network evaluator using ONNX Runtime for chess position evaluation
/// Replaces traditional hardcoded material values with learned evaluation
/// </summary>
public class NeuralNetworkEvaluator : IDisposable
{
    private readonly InferenceSession _session;
    private readonly string _inputName;
    private readonly string _outputName;
    private readonly ConcurrentQueue<float[]> _inputBufferPool;
    private readonly object _sessionLock = new();
    
    // King bucket mapping for the 12 factorized buckets
    private static readonly int[] KingBuckets = new int[64];
    
    static NeuralNetworkEvaluator()
    {
        InitializeKingBuckets();
    }

    public NeuralNetworkEvaluator(string onnxModelPath)
    {
        var sessionOptions = new SessionOptions
        {
            EnableCpuMemArena = true,
            EnableMemoryPattern = true,
            GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_ALL
        };

        _session = new InferenceSession(onnxModelPath, sessionOptions);
        
        // Get input/output names
        _inputName = _session.InputMetadata.Keys.First();
        _outputName = _session.OutputMetadata.Keys.First();
        
        // Initialize buffer pool for thread safety
        _inputBufferPool = new ConcurrentQueue<float[]>();
        for (int i = 0; i < Environment.ProcessorCount * 2; i++)
        {
            _inputBufferPool.Enqueue(new float[768]);
        }
    }

    public int Evaluate(Board board)
    {
        // Get input buffer from pool
        if (!_inputBufferPool.TryDequeue(out var inputBuffer))
        {
            inputBuffer = new float[768];
        }

        try
        {
            // Convert board to neural network input features
            ConvertBoardToFeatures(board, inputBuffer);

            // Run inference
            var output = RunInference(inputBuffer);

            // Convert output to centipawn score
            return ConvertOutputToScore(output, board);
        }
        finally
        {
            // Return buffer to pool
            Array.Clear(inputBuffer);
            _inputBufferPool.Enqueue(inputBuffer);
        }
    }

    private void ConvertBoardToFeatures(Board board, float[] features)
    {
        Array.Clear(features);

        // Get king positions for bucket calculation
        var whiteKingBitboard = board.GetPieceBitboard(PieceType.King, Color.White);
        var blackKingBitboard = board.GetPieceBitboard(PieceType.King, Color.Black);
        
        int whiteKingSquare = whiteKingBitboard != 0 ? Bitboards.TrailingZeroCount(whiteKingBitboard) : 4;
        int blackKingSquare = blackKingBitboard != 0 ? Bitboards.TrailingZeroCount(blackKingBitboard) : 60;

        // Calculate perspective-based features
        if (board.SideToMove == Color.White)
        {
            AddPieceFeatures(board, features, Color.White, whiteKingSquare, blackKingSquare);
        }
        else
        {
            AddPieceFeatures(board, features, Color.Black, blackKingSquare, whiteKingSquare);
        }
    }

    private static void AddPieceFeatures(Board board, float[] features, Color perspective, 
                                       int friendlyKingSquare, int enemyKingSquare)
    {
        int friendlyKingBucket = KingBuckets[friendlyKingSquare];
        
        // Process all piece types for both colors
        for (int pieceType = 1; pieceType <= 6; pieceType++)
        {
            var piece = (PieceType)pieceType;
            
            // Friendly pieces
            var friendlyPieces = board.GetPieceBitboard(piece, perspective);
            AddPiecesToFeatures(features, friendlyPieces, piece, perspective, friendlyKingBucket, true);
            
            // Enemy pieces
            var enemyColor = perspective == Color.White ? Color.Black : Color.White;
            var enemyPieces = board.GetPieceBitboard(piece, enemyColor);
            AddPiecesToFeatures(features, enemyPieces, piece, enemyColor, friendlyKingBucket, false);
        }
    }

    private static void AddPiecesToFeatures(float[] features, ulong pieceBitboard, PieceType pieceType, 
                                          Color color, int kingBucket, bool isFriendly)
    {
        while (pieceBitboard != 0)
        {
            int square = Bitboards.PopLsb(ref pieceBitboard);
            
            // Calculate feature index based on:
            // - Piece type (6 types)
            // - Color (2 colors) 
            // - Square (64 squares)
            // - King bucket (12 buckets)
            
            int pieceIndex = (int)pieceType - 1; // 0-5
            int colorIndex = isFriendly ? 0 : 1;
            
            // Feature index calculation for HalfKP-style features
            int featureIndex = square + 
                             (pieceIndex * 64) + 
                             (colorIndex * 64 * 6);
            
            if (featureIndex < 768)
            {
                features[featureIndex] = 1.0f;
            }
        }
    }

    private float[] RunInference(float[] inputBuffer)
    {
        var inputTensor = new DenseTensor<float>(inputBuffer, new[] { 1, 768 });
        var inputs = new List<NamedOnnxValue>
        {
            NamedOnnxValue.CreateFromTensor(_inputName, inputTensor)
        };

        lock (_sessionLock)
        {
            using var results = _session.Run(inputs);
            var outputTensor = results.First().AsTensor<float>();
            
            // Copy output to avoid disposal issues
            var output = new float[outputTensor.Length];
            outputTensor.ToArray().CopyTo(output, 0);
            return output;
        }
    }

    private static int ConvertOutputToScore(float[] output, Board board)
    {
        // The network outputs 8 values for different game phase buckets
        // We need to select the appropriate bucket based on game phase
        
        int materialCount = Bitboards.PopCount(board.GetAllPieces());
        int bucketIndex = CalculateOutputBucket(materialCount);
        
        // Clamp bucket index
        bucketIndex = Math.Max(0, Math.Min(7, bucketIndex));
        
        // Convert network output to centipawn score
        float rawScore = output[bucketIndex];
        
        // Scale the output (typical NNUE scaling)
        int centipawnScore = (int)(rawScore * 600.0f); // Adjust scaling as needed
        
        // Clamp to reasonable bounds
        return Math.Max(-30000, Math.Min(30000, centipawnScore));
    }

    private static int CalculateOutputBucket(int materialCount)
    {
        // Map material count to output bucket (0-7)
        // More pieces = earlier game phase = lower bucket index
        return materialCount switch
        {
            >= 30 => 0, // Opening
            >= 26 => 1,
            >= 22 => 2,
            >= 18 => 3, // Middlegame
            >= 14 => 4,
            >= 10 => 5,
            >= 6 => 6,  // Endgame
            _ => 7      // Late endgame
        };
    }

    private static void InitializeKingBuckets()
    {
        // Initialize king buckets for the 12 factorized buckets
        // This is a simplified mapping - real NNUE uses more sophisticated bucketing
        for (int square = 0; square < 64; square++)
        {
            int file = Bitboards.GetFile(square);
            int rank = Bitboards.GetRank(square);
            
            // Map to 12 buckets based on king position
            if (file <= 3) // Queenside
            {
                if (rank <= 1) KingBuckets[square] = 0;      // Back rank queenside
                else if (rank <= 3) KingBuckets[square] = 1; // Low queenside
                else if (rank <= 5) KingBuckets[square] = 2; // Mid queenside
                else KingBuckets[square] = 3;                // High queenside
            }
            else // Kingside
            {
                if (rank <= 1) KingBuckets[square] = 4;      // Back rank kingside
                else if (rank <= 3) KingBuckets[square] = 5; // Low kingside
                else if (rank <= 5) KingBuckets[square] = 6; // Mid kingside
                else KingBuckets[square] = 7;                // High kingside
            }
            
            // Center files get special buckets
            if (file >= 2 && file <= 5)
            {
                if (rank <= 3) KingBuckets[square] = 8;      // Center low
                else if (rank <= 5) KingBuckets[square] = 9; // Center mid
                else KingBuckets[square] = 10;               // Center high
            }
            
            // Special bucket for very central positions
            if (file >= 3 && file <= 4 && rank >= 3 && rank <= 4)
            {
                KingBuckets[square] = 11;
            }
        }
    }

    public void Dispose()
    {
        _session?.Dispose();
        GC.SuppressFinalize(this);
    }
}
