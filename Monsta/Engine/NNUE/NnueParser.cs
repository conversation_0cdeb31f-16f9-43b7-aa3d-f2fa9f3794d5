using System.Text;

namespace Monsta.Engine.NNUE;

/// <summary>
/// Parser for NNUE (Efficiently Updatable Neural Network) files
/// Based on the Integral chess engine format
/// </summary>
public class NnueParser
{
    public class NnueNetwork
    {
        public required NnueFeatureTransformer FeatureTransformer { get; set; }
        public required NnueLayer[] HiddenLayers { get; set; }
        public required NnueOutputLayer OutputLayer { get; set; }
        public required NnueHeader Header { get; set; }
    }

    public class NnueHeader
    {
        public uint Version { get; set; }
        public uint HashValue { get; set; }
        public string Description { get; set; } = "";
        public uint NetworkSize { get; set; }
    }

    public class NnueFeatureTransformer
    {
        public required float[] Weights { get; set; }  // 768x12 factorized weights
        public required float[] Biases { get; set; }   // 1536 biases
        public int InputSize { get; set; } = 768;
        public int OutputSize { get; set; } = 1536;
        public int NumBuckets { get; set; } = 12;
    }

    public class NnueLayer
    {
        public required float[] Weights { get; set; }
        public required float[] Biases { get; set; }
        public int InputSize { get; set; }
        public int OutputSize { get; set; }
    }

    public class NnueOutputLayer
    {
        public required float[] Weights { get; set; }
        public required float[] Biases { get; set; }
        public int InputSize { get; set; } = 32;
        public int OutputSize { get; set; } = 8; // 8 output buckets
    }

    public static NnueNetwork ParseNnueFile(string filePath)
    {
        using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
        using var reader = new BinaryReader(fileStream);

        var header = ParseHeader(reader);
        var featureTransformer = ParseFeatureTransformer(reader);
        var hiddenLayers = ParseHiddenLayers(reader);
        var outputLayer = ParseOutputLayer(reader);

        return new NnueNetwork
        {
            Header = header,
            FeatureTransformer = featureTransformer,
            HiddenLayers = hiddenLayers,
            OutputLayer = outputLayer
        };
    }

    private static NnueHeader ParseHeader(BinaryReader reader)
    {
        var header = new NnueHeader();
        
        // Read magic number and version
        var magic = reader.ReadUInt32();
        header.Version = reader.ReadUInt32();
        header.HashValue = reader.ReadUInt32();
        
        // Read description length and description
        var descLength = reader.ReadUInt32();
        if (descLength > 0 && descLength < 1024) // Sanity check
        {
            var descBytes = reader.ReadBytes((int)descLength);
            header.Description = Encoding.UTF8.GetString(descBytes).TrimEnd('\0');
        }
        
        header.NetworkSize = reader.ReadUInt32();
        
        return header;
    }

    private static NnueFeatureTransformer ParseFeatureTransformer(BinaryReader reader)
    {
        // Architecture: 768x12 (Factorized) -> 1536
        const int inputSize = 768;
        const int outputSize = 1536;
        const int numBuckets = 12;
        
        // Read factorized weights: 768 * 12 * (1536/12) = 768 * 1536
        var weightsSize = inputSize * outputSize;
        var weights = new float[weightsSize];
        
        for (int i = 0; i < weightsSize; i++)
        {
            weights[i] = reader.ReadInt16() / 64.0f; // NNUE typically uses int16 with scaling
        }
        
        // Read biases
        var biases = new float[outputSize];
        for (int i = 0; i < outputSize; i++)
        {
            biases[i] = reader.ReadInt32() / 16384.0f; // Different scaling for biases
        }
        
        return new NnueFeatureTransformer
        {
            Weights = weights,
            Biases = biases,
            InputSize = inputSize,
            OutputSize = outputSize,
            NumBuckets = numBuckets
        };
    }

    private static NnueLayer[] ParseHiddenLayers(BinaryReader reader)
    {
        var layers = new List<NnueLayer>();
        
        // Layer 1: 1536*2 -> 16
        var layer1 = ParseLayer(reader, 1536 * 2, 16);
        layers.Add(layer1);
        
        // Layer 2: 16 -> 32
        var layer2 = ParseLayer(reader, 16, 32);
        layers.Add(layer2);
        
        return layers.ToArray();
    }

    private static NnueLayer ParseLayer(BinaryReader reader, int inputSize, int outputSize)
    {
        var weightsSize = inputSize * outputSize;
        var weights = new float[weightsSize];
        
        for (int i = 0; i < weightsSize; i++)
        {
            weights[i] = reader.ReadInt8() / 64.0f; // int8 weights for hidden layers
        }
        
        var biases = new float[outputSize];
        for (int i = 0; i < outputSize; i++)
        {
            biases[i] = reader.ReadInt32() / 16384.0f;
        }
        
        return new NnueLayer
        {
            Weights = weights,
            Biases = biases,
            InputSize = inputSize,
            OutputSize = outputSize
        };
    }

    private static NnueOutputLayer ParseOutputLayer(BinaryReader reader)
    {
        // Final layer: 32 -> 1 (but with 8 output buckets)
        const int inputSize = 32;
        const int outputSize = 8;
        
        var weightsSize = inputSize * outputSize;
        var weights = new float[weightsSize];
        
        for (int i = 0; i < weightsSize; i++)
        {
            weights[i] = reader.ReadInt8() / 64.0f;
        }
        
        var biases = new float[outputSize];
        for (int i = 0; i < outputSize; i++)
        {
            biases[i] = reader.ReadInt32() / 16384.0f;
        }
        
        return new NnueOutputLayer
        {
            Weights = weights,
            Biases = biases,
            InputSize = inputSize,
            OutputSize = outputSize
        };
    }
}

/// <summary>
/// Extension methods for BinaryReader to handle different data types
/// </summary>
public static class BinaryReaderExtensions
{
    public static sbyte ReadInt8(this BinaryReader reader)
    {
        return (sbyte)reader.ReadByte();
    }
}
