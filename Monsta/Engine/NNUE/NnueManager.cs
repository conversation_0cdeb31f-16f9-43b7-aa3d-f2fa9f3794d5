namespace Monsta.Engine.NNUE;

/// <summary>
/// Manages NNUE model loading, conversion, and initialization
/// </summary>
public static class NnueManager
{
    private static NeuralNetworkEvaluator? _evaluator;
    private static readonly object _lock = new();

    public static void Initialize(string nnuePath)
    {
        lock (_lock)
        {
            if (_evaluator != null)
            {
                _evaluator.Dispose();
            }

            try
            {
                // Check if ONNX model already exists
                string onnxPath = Path.ChangeExtension(nnuePath, ".onnx");
                
                if (!File.Exists(onnxPath) || File.GetLastWriteTime(nnuePath) > File.GetLastWriteTime(onnxPath))
                {
                    Console.WriteLine("info string Converting NNUE to ONNX format...");
                    ConvertNnueToOnnx(nnuePath, onnxPath);
                    Console.WriteLine("info string NNUE conversion completed");
                }

                Console.WriteLine("info string Loading neural network evaluator...");
                _evaluator = new NeuralNetworkEvaluator(onnxPath);
                Console.WriteLine("info string Neural network evaluator loaded successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"info string Failed to initialize NNUE: {ex.Message}");
                Console.WriteLine("info string Falling back to traditional evaluation");
                _evaluator = null;
            }
        }
    }

    public static int Evaluate(Board board)
    {
        lock (_lock)
        {
            if (_evaluator != null)
            {
                try
                {
                    return _evaluator.Evaluate(board);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"info string NNUE evaluation error: {ex.Message}");
                    Console.WriteLine("info string Falling back to traditional evaluation");
                    
                    // Dispose failed evaluator
                    _evaluator.Dispose();
                    _evaluator = null;
                }
            }

            // Fallback to traditional evaluation if NNUE fails
            return TraditionalEvaluation.Evaluate(board);
        }
    }

    public static bool IsNnueAvailable => _evaluator != null;

    public static void Dispose()
    {
        lock (_lock)
        {
            _evaluator?.Dispose();
            _evaluator = null;
        }
    }

    private static void ConvertNnueToOnnx(string nnuePath, string onnxPath)
    {
        try
        {
            // Parse the NNUE file
            var network = NnueParser.ParseNnueFile(nnuePath);
            
            // Convert to ONNX
            NnueToOnnxConverter.ConvertToOnnx(network, onnxPath);
            
            Console.WriteLine($"info string Converted {nnuePath} to {onnxPath}");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to convert NNUE to ONNX: {ex.Message}", ex);
        }
    }
}

/// <summary>
/// Simplified traditional evaluation as fallback
/// </summary>
public static class TraditionalEvaluation
{
    private static readonly int[] PieceValues = { 0, 100, 320, 330, 500, 900, 20000 };

    public static int Evaluate(Board board)
    {
        int score = 0;
        
        // Simple material evaluation
        for (int pieceType = 1; pieceType <= 6; pieceType++)
        {
            ulong whitePieces = board.GetPieceBitboard((PieceType)pieceType, Color.White);
            ulong blackPieces = board.GetPieceBitboard((PieceType)pieceType, Color.Black);
            
            int whiteCount = Bitboards.PopCount(whitePieces);
            int blackCount = Bitboards.PopCount(blackPieces);
            
            score += (whiteCount - blackCount) * PieceValues[pieceType];
        }

        // Simple mobility bonus
        var moves = MoveGenerator.GenerateMoves(board);
        score += moves.Count * 2;

        // Return score from the perspective of the side to move
        return board.SideToMove == Color.White ? score : -score;
    }
}
