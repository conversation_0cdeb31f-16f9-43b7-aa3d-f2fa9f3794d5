using System.Numerics;
using System.Runtime.CompilerServices;

namespace Monsta.Engine;

/// <summary>
/// Bitboard utilities for efficient chess operations
/// </summary>
public static class Bitboards
{
    // File and rank masks
    public static readonly ulong[] FileMasks = new ulong[8];
    public static readonly ulong[] RankMasks = new ulong[8];
    
    // Direction masks
    public static readonly ulong[] DiagonalMasks = new ulong[15];
    public static readonly ulong[] AntiDiagonalMasks = new ulong[15];
    
    // Square masks
    public static readonly ulong[] SquareMasks = new ulong[64];
    
    // Knight attack masks
    public static readonly ulong[] KnightAttacks = new ulong[64];
    
    // King attack masks
    public static readonly ulong[] KingAttacks = new ulong[64];
    
    // Pawn attack masks
    public static readonly ulong[] WhitePawnAttacks = new ulong[64];
    public static readonly ulong[] BlackPawnAttacks = new ulong[64];
    
    // Constants
    public const ulong FileA = 0x0101010101010101UL;
    public const ulong FileB = 0x0202020202020202UL;
    public const ulong FileG = 0x4040404040404040UL;
    public const ulong FileH = 0x8080808080808080UL;
    
    public const ulong Rank1 = 0x00000000000000FFUL;
    public const ulong Rank2 = 0x000000000000FF00UL;
    public const ulong Rank7 = 0x00FF000000000000UL;
    public const ulong Rank8 = 0xFF00000000000000UL;
    
    public const ulong LightSquares = 0x55AA55AA55AA55AAUL;
    public const ulong DarkSquares = 0xAA55AA55AA55AA55UL;
    
    static Bitboards()
    {
        InitializeSquareMasks();
        InitializeFilesAndRanks();
        InitializeDiagonals();
        InitializeKnightAttacks();
        InitializeKingAttacks();
        InitializePawnAttacks();
    }
    
    private static void InitializeSquareMasks()
    {
        for (int square = 0; square < 64; square++)
        {
            SquareMasks[square] = 1UL << square;
        }
    }
    
    private static void InitializeFilesAndRanks()
    {
        for (int file = 0; file < 8; file++)
        {
            FileMasks[file] = FileA << file;
        }
        
        for (int rank = 0; rank < 8; rank++)
        {
            RankMasks[rank] = Rank1 << (rank * 8);
        }
    }
    
    private static void InitializeDiagonals()
    {
        // Main diagonals (bottom-left to top-right)
        for (int i = 0; i < 15; i++)
        {
            ulong diagonal = 0;
            int startFile = Math.Max(0, i - 7);
            int startRank = Math.Max(0, 7 - i);
            
            for (int j = 0; j < 8; j++)
            {
                int file = startFile + j;
                int rank = startRank + j;
                if (file >= 8 || rank >= 8) break;
                diagonal |= 1UL << (rank * 8 + file);
            }
            DiagonalMasks[i] = diagonal;
        }
        
        // Anti-diagonals (top-left to bottom-right)
        for (int i = 0; i < 15; i++)
        {
            ulong antiDiagonal = 0;
            int startFile = Math.Max(0, i - 7);
            int startRank = Math.Min(7, i);
            
            for (int j = 0; j < 8; j++)
            {
                int file = startFile + j;
                int rank = startRank - j;
                if (file >= 8 || rank < 0) break;
                antiDiagonal |= 1UL << (rank * 8 + file);
            }
            AntiDiagonalMasks[i] = antiDiagonal;
        }
    }
    
    private static void InitializeKnightAttacks()
    {
        int[] knightMoves = { -17, -15, -10, -6, 6, 10, 15, 17 };
        
        for (int square = 0; square < 64; square++)
        {
            ulong attacks = 0;
            int file = square & 7;
            int rank = square >> 3;
            
            foreach (int move in knightMoves)
            {
                int targetSquare = square + move;
                if (targetSquare < 0 || targetSquare >= 64) continue;
                
                int targetFile = targetSquare & 7;
                int targetRank = targetSquare >> 3;
                
                // Check if the move wraps around the board
                if (Math.Abs(file - targetFile) <= 2 && Math.Abs(rank - targetRank) <= 2)
                {
                    attacks |= 1UL << targetSquare;
                }
            }
            KnightAttacks[square] = attacks;
        }
    }
    
    private static void InitializeKingAttacks()
    {
        int[] kingMoves = { -9, -8, -7, -1, 1, 7, 8, 9 };
        
        for (int square = 0; square < 64; square++)
        {
            ulong attacks = 0;
            int file = square & 7;
            int rank = square >> 3;
            
            foreach (int move in kingMoves)
            {
                int targetSquare = square + move;
                if (targetSquare < 0 || targetSquare >= 64) continue;
                
                int targetFile = targetSquare & 7;
                int targetRank = targetSquare >> 3;
                
                // Check if the move wraps around the board
                if (Math.Abs(file - targetFile) <= 1 && Math.Abs(rank - targetRank) <= 1)
                {
                    attacks |= 1UL << targetSquare;
                }
            }
            KingAttacks[square] = attacks;
        }
    }
    
    private static void InitializePawnAttacks()
    {
        for (int square = 0; square < 64; square++)
        {
            int file = square & 7;
            int rank = square >> 3;
            
            // White pawn attacks
            if (rank < 7)
            {
                if (file > 0) WhitePawnAttacks[square] |= 1UL << (square + 7);
                if (file < 7) WhitePawnAttacks[square] |= 1UL << (square + 9);
            }
            
            // Black pawn attacks
            if (rank > 0)
            {
                if (file > 0) BlackPawnAttacks[square] |= 1UL << (square - 9);
                if (file < 7) BlackPawnAttacks[square] |= 1UL << (square - 7);
            }
        }
    }
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int PopCount(ulong bitboard) => BitOperations.PopCount(bitboard);
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int TrailingZeroCount(ulong bitboard) => BitOperations.TrailingZeroCount(bitboard);
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int PopLsb(ref ulong bitboard)
    {
        int square = TrailingZeroCount(bitboard);
        bitboard &= bitboard - 1;
        return square;
    }
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong GetBit(ulong bitboard, int square) => bitboard & (1UL << square);
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong SetBit(ulong bitboard, int square) => bitboard | (1UL << square);
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong ClearBit(ulong bitboard, int square) => bitboard & ~(1UL << square);
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int GetFile(int square) => square & 7;
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int GetRank(int square) => square >> 3;
    
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int MakeSquare(int file, int rank) => rank * 8 + file;
    
    public static string BitboardToString(ulong bitboard)
    {
        var result = new System.Text.StringBuilder();
        for (int rank = 7; rank >= 0; rank--)
        {
            for (int file = 0; file < 8; file++)
            {
                int square = rank * 8 + file;
                result.Append(GetBit(bitboard, square) != 0 ? '1' : '0');
                result.Append(' ');
            }
            result.AppendLine();
        }
        return result.ToString();
    }
}
