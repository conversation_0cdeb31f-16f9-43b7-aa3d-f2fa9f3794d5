using Monsta.Engine.NNUE;

namespace Monsta.Engine;

/// <summary>
/// Position evaluation function for the chess engine
/// Now uses Neural Network evaluation (NNUE) instead of hardcoded material values
/// </summary>
public static class Evaluation
{
    public static int Evaluate(Board board)
    {
        // Use neural network evaluation if available
        return NnueManager.Evaluate(board);
    }

    /// <summary>
    /// Initialize the evaluation system with NNUE model
    /// </summary>
    public static void Initialize(string? nnuePath = null)
    {
        nnuePath ??= "zekrom-v6.nnue";

        if (File.Exists(nnuePath))
        {
            Console.WriteLine($"info string Initializing NNUE evaluation with {nnuePath}");
            NnueManager.Initialize(nnuePath);
        }
        else
        {
            Console.WriteLine($"info string NNUE file not found: {nnuePath}");
            Console.WriteLine("info string Using traditional evaluation");
        }
    }

    /// <summary>
    /// Cleanup evaluation resources
    /// </summary>
    public static void Cleanup()
    {
        NnueManager.Dispose();
    }

    // Legacy evaluation methods kept for reference/fallback
    // These are now only used if NNUE initialization fails

    // Piece values in centipawns (legacy)
    private static readonly int[] PieceValues = { 0, 100, 320, 330, 500, 900, 20000 };

    // Piece-square tables for positional evaluation (legacy)
    private static readonly int[,] PawnTable = {
        {  0,  0,  0,  0,  0,  0,  0,  0 },
        { 50, 50, 50, 50, 50, 50, 50, 50 },
        { 10, 10, 20, 30, 30, 20, 10, 10 },
        {  5,  5, 10, 25, 25, 10,  5,  5 },
        {  0,  0,  0, 20, 20,  0,  0,  0 },
        {  5, -5,-10,  0,  0,-10, -5,  5 },
        {  5, 10, 10,-20,-20, 10, 10,  5 },
        {  0,  0,  0,  0,  0,  0,  0,  0 }
    };

    private static readonly int[,] KnightTable = {
        { -50,-40,-30,-30,-30,-30,-40,-50 },
        { -40,-20,  0,  0,  0,  0,-20,-40 },
        { -30,  0, 10, 15, 15, 10,  0,-30 },
        { -30,  5, 15, 20, 20, 15,  5,-30 },
        { -30,  0, 15, 20, 20, 15,  0,-30 },
        { -30,  5, 10, 15, 15, 10,  5,-30 },
        { -40,-20,  0,  5,  5,  0,-20,-40 },
        { -50,-40,-30,-30,-30,-30,-40,-50 }
    };

    private static readonly int[,] BishopTable = {
        { -20,-10,-10,-10,-10,-10,-10,-20 },
        { -10,  0,  0,  0,  0,  0,  0,-10 },
        { -10,  0,  5, 10, 10,  5,  0,-10 },
        { -10,  5,  5, 10, 10,  5,  5,-10 },
        { -10,  0, 10, 10, 10, 10,  0,-10 },
        { -10, 10, 10, 10, 10, 10, 10,-10 },
        { -10,  5,  0,  0,  0,  0,  5,-10 },
        { -20,-10,-10,-10,-10,-10,-10,-20 }
    };

    private static readonly int[,] RookTable = {
        {  0,  0,  0,  0,  0,  0,  0,  0 },
        {  5, 10, 10, 10, 10, 10, 10,  5 },
        { -5,  0,  0,  0,  0,  0,  0, -5 },
        { -5,  0,  0,  0,  0,  0,  0, -5 },
        { -5,  0,  0,  0,  0,  0,  0, -5 },
        { -5,  0,  0,  0,  0,  0,  0, -5 },
        { -5,  0,  0,  0,  0,  0,  0, -5 },
        {  0,  0,  0,  5,  5,  0,  0,  0 }
    };

    private static readonly int[,] QueenTable = {
        { -20,-10,-10, -5, -5,-10,-10,-20 },
        { -10,  0,  0,  0,  0,  0,  0,-10 },
        { -10,  0,  5,  5,  5,  5,  0,-10 },
        {  -5,  0,  5,  5,  5,  5,  0, -5 },
        {   0,  0,  5,  5,  5,  5,  0, -5 },
        { -10,  5,  5,  5,  5,  5,  0,-10 },
        { -10,  0,  5,  0,  0,  0,  0,-10 },
        { -20,-10,-10, -5, -5,-10,-10,-20 }
    };

    private static readonly int[,] KingMiddleGameTable = {
        { -30,-40,-40,-50,-50,-40,-40,-30 },
        { -30,-40,-40,-50,-50,-40,-40,-30 },
        { -30,-40,-40,-50,-50,-40,-40,-30 },
        { -30,-40,-40,-50,-50,-40,-40,-30 },
        { -20,-30,-30,-40,-40,-30,-30,-20 },
        { -10,-20,-20,-20,-20,-20,-20,-10 },
        {  20, 20,  0,  0,  0,  0, 20, 20 },
        {  20, 30, 10,  0,  0, 10, 30, 20 }
    };

    private static readonly int[,] KingEndGameTable = {
        { -50,-40,-30,-20,-20,-30,-40,-50 },
        { -30,-20,-10,  0,  0,-10,-20,-30 },
        { -30,-10, 20, 30, 30, 20,-10,-30 },
        { -30,-10, 30, 40, 40, 30,-10,-30 },
        { -30,-10, 30, 40, 40, 30,-10,-30 },
        { -30,-10, 20, 30, 30, 20,-10,-30 },
        { -30,-30,  0,  0,  0,  0,-30,-30 },
        { -50,-30,-30,-30,-30,-30,-30,-50 }
    };

    public static int Evaluate(Board board)
    {
        int score = 0;
        
        // Material and positional evaluation
        score += EvaluateMaterial(board);
        score += EvaluatePosition(board);
        score += EvaluatePawnStructure(board);
        score += EvaluateKingSafety(board);
        score += EvaluateMobility(board);

        // Return score from the perspective of the side to move
        return board.SideToMove == Color.White ? score : -score;
    }

    private static int EvaluateMaterial(Board board)
    {
        int score = 0;
        
        for (int pieceType = 1; pieceType <= 6; pieceType++)
        {
            ulong whitePieces = board.GetPieceBitboard((PieceType)pieceType, Color.White);
            ulong blackPieces = board.GetPieceBitboard((PieceType)pieceType, Color.Black);
            
            int whiteCount = Bitboards.PopCount(whitePieces);
            int blackCount = Bitboards.PopCount(blackPieces);
            
            score += (whiteCount - blackCount) * PieceValues[pieceType];
        }
        
        return score;
    }

    private static int EvaluatePosition(Board board)
    {
        int score = 0;
        bool isEndGame = IsEndGame(board);
        
        // Evaluate each piece type
        score += EvaluatePieceSquares(board, PieceType.Pawn, PawnTable);
        score += EvaluatePieceSquares(board, PieceType.Knight, KnightTable);
        score += EvaluatePieceSquares(board, PieceType.Bishop, BishopTable);
        score += EvaluatePieceSquares(board, PieceType.Rook, RookTable);
        score += EvaluatePieceSquares(board, PieceType.Queen, QueenTable);
        
        // King evaluation depends on game phase
        var kingTable = isEndGame ? KingEndGameTable : KingMiddleGameTable;
        score += EvaluatePieceSquares(board, PieceType.King, kingTable);
        
        return score;
    }

    private static int EvaluatePieceSquares(Board board, PieceType pieceType, int[,] table)
    {
        int score = 0;
        
        ulong whitePieces = board.GetPieceBitboard(pieceType, Color.White);
        ulong blackPieces = board.GetPieceBitboard(pieceType, Color.Black);
        
        while (whitePieces != 0)
        {
            int square = Bitboards.PopLsb(ref whitePieces);
            int file = Bitboards.GetFile(square);
            int rank = Bitboards.GetRank(square);
            score += table[rank, file];
        }
        
        while (blackPieces != 0)
        {
            int square = Bitboards.PopLsb(ref blackPieces);
            int file = Bitboards.GetFile(square);
            int rank = 7 - Bitboards.GetRank(square); // Flip for black
            score -= table[rank, file];
        }
        
        return score;
    }

    private static int EvaluatePawnStructure(Board board)
    {
        int score = 0;
        
        ulong whitePawns = board.GetPieceBitboard(PieceType.Pawn, Color.White);
        ulong blackPawns = board.GetPieceBitboard(PieceType.Pawn, Color.Black);
        
        // Doubled pawns penalty
        for (int file = 0; file < 8; file++)
        {
            ulong fileMask = Bitboards.FileMasks[file];
            int whiteCount = Bitboards.PopCount(whitePawns & fileMask);
            int blackCount = Bitboards.PopCount(blackPawns & fileMask);
            
            if (whiteCount > 1) score -= (whiteCount - 1) * 10;
            if (blackCount > 1) score += (blackCount - 1) * 10;
        }
        
        // Isolated pawns penalty
        score += EvaluateIsolatedPawns(whitePawns, blackPawns);
        
        // Passed pawns bonus
        score += EvaluatePassedPawns(board, whitePawns, blackPawns);
        
        return score;
    }

    private static int EvaluateIsolatedPawns(ulong whitePawns, ulong blackPawns)
    {
        int score = 0;
        
        for (int file = 0; file < 8; file++)
        {
            ulong fileMask = Bitboards.FileMasks[file];
            ulong adjacentFiles = 0;
            
            if (file > 0) adjacentFiles |= Bitboards.FileMasks[file - 1];
            if (file < 7) adjacentFiles |= Bitboards.FileMasks[file + 1];
            
            // Check white pawns
            if ((whitePawns & fileMask) != 0 && (whitePawns & adjacentFiles) == 0)
                score -= 20; // Isolated pawn penalty
            
            // Check black pawns
            if ((blackPawns & fileMask) != 0 && (blackPawns & adjacentFiles) == 0)
                score += 20; // Isolated pawn penalty for black
        }
        
        return score;
    }

    private static int EvaluatePassedPawns(Board board, ulong whitePawns, ulong blackPawns)
    {
        int score = 0;
        
        // Check white passed pawns
        ulong whitePawnsCopy = whitePawns;
        while (whitePawnsCopy != 0)
        {
            int square = Bitboards.PopLsb(ref whitePawnsCopy);
            if (IsPassedPawn(square, Color.White, whitePawns, blackPawns))
            {
                int rank = Bitboards.GetRank(square);
                score += (rank - 1) * 10; // Bonus increases with advancement
            }
        }
        
        // Check black passed pawns
        ulong blackPawnsCopy = blackPawns;
        while (blackPawnsCopy != 0)
        {
            int square = Bitboards.PopLsb(ref blackPawnsCopy);
            if (IsPassedPawn(square, Color.Black, whitePawns, blackPawns))
            {
                int rank = Bitboards.GetRank(square);
                score -= (6 - rank) * 10; // Bonus increases with advancement
            }
        }
        
        return score;
    }

    private static bool IsPassedPawn(int square, Color color, ulong whitePawns, ulong blackPawns)
    {
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);
        
        ulong blockingMask = 0;
        ulong enemyPawns = color == Color.White ? blackPawns : whitePawns;
        
        // Create mask for squares in front of the pawn
        if (color == Color.White)
        {
            for (int r = rank + 1; r < 8; r++)
            {
                if (file > 0) blockingMask |= 1UL << Bitboards.MakeSquare(file - 1, r);
                blockingMask |= 1UL << Bitboards.MakeSquare(file, r);
                if (file < 7) blockingMask |= 1UL << Bitboards.MakeSquare(file + 1, r);
            }
        }
        else
        {
            for (int r = rank - 1; r >= 0; r--)
            {
                if (file > 0) blockingMask |= 1UL << Bitboards.MakeSquare(file - 1, r);
                blockingMask |= 1UL << Bitboards.MakeSquare(file, r);
                if (file < 7) blockingMask |= 1UL << Bitboards.MakeSquare(file + 1, r);
            }
        }
        
        return (enemyPawns & blockingMask) == 0;
    }

    private static int EvaluateKingSafety(Board board)
    {
        int score = 0;
        
        // Simple king safety evaluation based on pawn shield
        ulong whiteKing = board.GetPieceBitboard(PieceType.King, Color.White);
        ulong blackKing = board.GetPieceBitboard(PieceType.King, Color.Black);
        
        if (whiteKing != 0)
        {
            int kingSquare = Bitboards.TrailingZeroCount(whiteKing);
            score += EvaluatePawnShield(board, kingSquare, Color.White);
        }
        
        if (blackKing != 0)
        {
            int kingSquare = Bitboards.TrailingZeroCount(blackKing);
            score -= EvaluatePawnShield(board, kingSquare, Color.Black);
        }
        
        return score;
    }

    private static int EvaluatePawnShield(Board board, int kingSquare, Color color)
    {
        int score = 0;
        int file = Bitboards.GetFile(kingSquare);
        int rank = Bitboards.GetRank(kingSquare);
        
        ulong pawns = board.GetPieceBitboard(PieceType.Pawn, color);
        int direction = color == Color.White ? 1 : -1;
        
        // Check pawn shield in front of king
        for (int f = Math.Max(0, file - 1); f <= Math.Min(7, file + 1); f++)
        {
            for (int r = rank + direction; r is >= 0 and < 8; r += direction)
            {
                int square = Bitboards.MakeSquare(f, r);
                if ((pawns & (1UL << square)) != 0)
                {
                    score += 10; // Bonus for pawn shield
                    break;
                }
            }
        }
        
        return score;
    }

    private static int EvaluateMobility(Board board)
    {
        int score = 0;

        // Simple mobility evaluation - count legal moves for current side
        var currentSideMoves = MoveGenerator.GenerateMoves(board);

        // Create a copy of the board to evaluate opponent mobility
        var fen = board.ToFen();
        var parts = fen.Split(' ');
        parts[1] = board.SideToMove == Color.White ? "b" : "w";
        var opponentBoard = new Board(string.Join(' ', parts));

        var opponentMoves = MoveGenerator.GenerateMoves(opponentBoard);

        // Calculate mobility difference
        score += (currentSideMoves.Count - opponentMoves.Count) * 2;

        return score;
    }

    private static bool IsEndGame(Board board)
    {
        // Simple endgame detection - few pieces remaining
        int totalPieces = Bitboards.PopCount(board.GetAllPieces());
        return totalPieces <= 12; // Arbitrary threshold
    }
}
