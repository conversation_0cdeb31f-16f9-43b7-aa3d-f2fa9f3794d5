namespace Monsta.Engine;

/// <summary>
/// Transposition table for caching search results
/// </summary>
public class TranspositionTable
{
    private readonly TranspositionEntry[] _table;
    private readonly int _size;

    public TranspositionTable(int sizeMB = 64)
    {
        int entrySize = System.Runtime.InteropServices.Marshal.SizeOf<TranspositionEntry>();
        _size = (sizeMB * 1024 * 1024) / entrySize;
        _table = new TranspositionEntry[_size];
    }

    public void Store(ulong hash, int depth, int score, NodeType nodeType, Move bestMove, int ply)
    {
        int index = (int)(hash % (ulong)_size);
        
        // Adjust mate scores to be relative to current position
        int adjustedScore = score;
        if (score > 19000) adjustedScore += ply;
        else if (score < -19000) adjustedScore -= ply;
        
        _table[index] = new TranspositionEntry
        {
            Hash = hash,
            Depth = (byte)depth,
            Score = (short)adjustedScore,
            NodeType = nodeType,
            BestMove = bestMove
        };
    }

    public bool Probe(ulong hash, int depth, int alpha, int beta, int ply, out int score, out Move bestMove)
    {
        score = 0;
        bestMove = Move.NullMove;
        
        int index = (int)(hash % (ulong)_size);
        ref var entry = ref _table[index];
        
        if (entry.Hash != hash)
            return false;
        
        bestMove = entry.BestMove;
        
        if (entry.Depth < depth)
            return false;
        
        // Adjust mate scores to be relative to current position
        int adjustedScore = entry.Score;
        if (adjustedScore > 19000) adjustedScore -= ply;
        else if (adjustedScore < -19000) adjustedScore += ply;
        
        switch (entry.NodeType)
        {
            case NodeType.Exact:
                score = adjustedScore;
                return true;
            
            case NodeType.LowerBound:
                if (adjustedScore >= beta)
                {
                    score = adjustedScore;
                    return true;
                }
                break;
            
            case NodeType.UpperBound:
                if (adjustedScore <= alpha)
                {
                    score = adjustedScore;
                    return true;
                }
                break;
        }
        
        return false;
    }

    public Move GetBestMove(ulong hash)
    {
        int index = (int)(hash % (ulong)_size);
        ref var entry = ref _table[index];
        
        return entry.Hash == hash ? entry.BestMove : Move.NullMove;
    }

    public void Clear()
    {
        Array.Clear(_table);
    }

    public int GetHashFull()
    {
        int filled = 0;
        int sampleSize = Math.Min(1000, _size);
        
        for (int i = 0; i < sampleSize; i++)
        {
            if (_table[i].Hash != 0)
                filled++;
        }
        
        return (filled * 1000) / sampleSize;
    }
}

public struct TranspositionEntry
{
    public ulong Hash;
    public byte Depth;
    public short Score;
    public NodeType NodeType;
    public Move BestMove;
}

public enum NodeType : byte
{
    Exact = 0,      // PV node - exact score
    LowerBound = 1, // Cut node - beta cutoff
    UpperBound = 2  // All node - alpha didn't improve
}
