using System.Text;

namespace Monsta.Engine;

/// <summary>
/// Represents a chess board position with bitboard-based representation
/// </summary>
public class Board
{
    // Piece bitboards
    private readonly ulong[] _pieces = new ulong[12]; // 6 piece types * 2 colors
    private readonly ulong[] _colors = new ulong[2];  // White and black occupancy
    private ulong _allPieces;

    // Board state
    public Color SideToMove { get; private set; }
    public int CastlingRights { get; private set; } // 4 bits: KQkq
    public int EnPassantSquare { get; private set; } = -1;
    public int HalfMoveClock { get; private set; }
    public int FullMoveNumber { get; private set; }

    // Zobrist hashing for transposition table
    public ulong Hash { get; private set; }

    // Constants for piece indexing
    private const int WhitePawn = 0, WhiteKnight = 1, WhiteBishop = 2, WhiteRook = 3, WhiteQueen = 4, WhiteKing = 5;
    private const int BlackPawn = 6, BlackKnight = 7, BlackBishop = 8, BlackRook = 9, BlackQueen = 10, BlackKing = 11;

    // Castling rights constants
    public const int WhiteKingSide = 1, WhiteQueenSide = 2, BlackKingSide = 4, BlackQueenSide = 8;

    public Board()
    {
        SetStartingPosition();
    }

    public Board(string fen)
    {
        SetFromFen(fen);
    }

    public void SetStartingPosition()
    {
        SetFromFen("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1");
    }

    public void SetFromFen(string fen)
    {
        Clear();
        
        var parts = fen.Split(' ');
        if (parts.Length != 6)
            throw new ArgumentException("Invalid FEN string");

        // Parse piece placement
        var ranks = parts[0].Split('/');
        for (int rank = 7; rank >= 0; rank--)
        {
            int file = 0;
            foreach (char c in ranks[7 - rank])
            {
                if (char.IsDigit(c))
                {
                    file += c - '0';
                }
                else
                {
                    var piece = CharToPiece(c);
                    var color = char.IsUpper(c) ? Color.White : Color.Black;
                    SetPiece(Bitboards.MakeSquare(file, rank), piece, color);
                    file++;
                }
            }
        }

        // Parse side to move
        SideToMove = parts[1] == "w" ? Color.White : Color.Black;

        // Parse castling rights
        CastlingRights = 0;
        if (parts[2].Contains('K')) CastlingRights |= WhiteKingSide;
        if (parts[2].Contains('Q')) CastlingRights |= WhiteQueenSide;
        if (parts[2].Contains('k')) CastlingRights |= BlackKingSide;
        if (parts[2].Contains('q')) CastlingRights |= BlackQueenSide;

        // Parse en passant square
        EnPassantSquare = parts[3] == "-" ? -1 : SquareFromString(parts[3]);

        // Parse move counters
        HalfMoveClock = int.Parse(parts[4]);
        FullMoveNumber = int.Parse(parts[5]);

        UpdateOccupancy();
        Hash = ZobristHash.ComputeHash(this);
    }

    public string ToFen()
    {
        var fen = new StringBuilder();

        // Piece placement
        for (int rank = 7; rank >= 0; rank--)
        {
            int emptyCount = 0;
            for (int file = 0; file < 8; file++)
            {
                int square = Bitboards.MakeSquare(file, rank);
                var (piece, color) = GetPiece(square);
                
                if (piece == PieceType.None)
                {
                    emptyCount++;
                }
                else
                {
                    if (emptyCount > 0)
                    {
                        fen.Append(emptyCount);
                        emptyCount = 0;
                    }
                    fen.Append(PieceToChar(piece, color));
                }
            }
            
            if (emptyCount > 0)
                fen.Append(emptyCount);
            
            if (rank > 0)
                fen.Append('/');
        }

        fen.Append(' ');
        fen.Append(SideToMove == Color.White ? 'w' : 'b');
        fen.Append(' ');

        // Castling rights
        if (CastlingRights == 0)
        {
            fen.Append('-');
        }
        else
        {
            if ((CastlingRights & WhiteKingSide) != 0) fen.Append('K');
            if ((CastlingRights & WhiteQueenSide) != 0) fen.Append('Q');
            if ((CastlingRights & BlackKingSide) != 0) fen.Append('k');
            if ((CastlingRights & BlackQueenSide) != 0) fen.Append('q');
        }

        fen.Append(' ');
        fen.Append(EnPassantSquare == -1 ? "-" : SquareToString(EnPassantSquare));
        fen.Append(' ');
        fen.Append(HalfMoveClock);
        fen.Append(' ');
        fen.Append(FullMoveNumber);

        return fen.ToString();
    }

    public ulong GetPieceBitboard(PieceType piece, Color color)
    {
        return _pieces[GetPieceIndex(piece, color)];
    }

    public ulong GetColorBitboard(Color color)
    {
        return _colors[(int)color];
    }

    public ulong GetAllPieces()
    {
        return _allPieces;
    }

    public (PieceType piece, Color color) GetPiece(int square)
    {
        ulong mask = 1UL << square;
        
        if ((_allPieces & mask) == 0)
            return (PieceType.None, Color.White);

        Color color = (_colors[0] & mask) != 0 ? Color.White : Color.Black;
        
        for (int pieceType = 1; pieceType <= 6; pieceType++)
        {
            if ((_pieces[GetPieceIndex((PieceType)pieceType, color)] & mask) != 0)
                return ((PieceType)pieceType, color);
        }

        return (PieceType.None, Color.White);
    }

    public void SetPiece(int square, PieceType piece, Color color)
    {
        ulong mask = 1UL << square;
        int pieceIndex = GetPieceIndex(piece, color);
        
        _pieces[pieceIndex] |= mask;
        _colors[(int)color] |= mask;
    }

    public void RemovePiece(int square)
    {
        ulong mask = ~(1UL << square);
        
        for (int i = 0; i < 12; i++)
            _pieces[i] &= mask;
        
        _colors[0] &= mask;
        _colors[1] &= mask;
    }

    public bool IsSquareAttacked(int square, Color attackingColor)
    {
        // Check pawn attacks
        ulong pawnAttacks = attackingColor == Color.White ? 
            Bitboards.BlackPawnAttacks[square] : Bitboards.WhitePawnAttacks[square];
        if ((pawnAttacks & GetPieceBitboard(PieceType.Pawn, attackingColor)) != 0)
            return true;

        // Check knight attacks
        if ((Bitboards.KnightAttacks[square] & GetPieceBitboard(PieceType.Knight, attackingColor)) != 0)
            return true;

        // Check king attacks
        if ((Bitboards.KingAttacks[square] & GetPieceBitboard(PieceType.King, attackingColor)) != 0)
            return true;

        // Check sliding piece attacks
        ulong occupancy = GetAllPieces();
        
        // Rook and queen attacks
        ulong rookAttacks = MagicBitboards.GetRookAttacks(square, occupancy);
        if ((rookAttacks & (GetPieceBitboard(PieceType.Rook, attackingColor) | 
                           GetPieceBitboard(PieceType.Queen, attackingColor))) != 0)
            return true;

        // Bishop and queen attacks
        ulong bishopAttacks = MagicBitboards.GetBishopAttacks(square, occupancy);
        if ((bishopAttacks & (GetPieceBitboard(PieceType.Bishop, attackingColor) | 
                             GetPieceBitboard(PieceType.Queen, attackingColor))) != 0)
            return true;

        return false;
    }

    public bool IsInCheck(Color color)
    {
        ulong kingBitboard = GetPieceBitboard(PieceType.King, color);
        if (kingBitboard == 0) return false;
        
        int kingSquare = Bitboards.TrailingZeroCount(kingBitboard);
        return IsSquareAttacked(kingSquare, color == Color.White ? Color.Black : Color.White);
    }

    private void Clear()
    {
        Array.Clear(_pieces);
        Array.Clear(_colors);
        _allPieces = 0;
        SideToMove = Color.White;
        CastlingRights = 0;
        EnPassantSquare = -1;
        HalfMoveClock = 0;
        FullMoveNumber = 1;
        Hash = 0;
    }

    private void UpdateOccupancy()
    {
        _colors[0] = _pieces[0] | _pieces[1] | _pieces[2] | _pieces[3] | _pieces[4] | _pieces[5];
        _colors[1] = _pieces[6] | _pieces[7] | _pieces[8] | _pieces[9] | _pieces[10] | _pieces[11];
        _allPieces = _colors[0] | _colors[1];
    }

    private static int GetPieceIndex(PieceType piece, Color color)
    {
        return (int)piece - 1 + (int)color * 6;
    }

    private static PieceType CharToPiece(char c) => char.ToUpper(c) switch
    {
        'P' => PieceType.Pawn,
        'N' => PieceType.Knight,
        'B' => PieceType.Bishop,
        'R' => PieceType.Rook,
        'Q' => PieceType.Queen,
        'K' => PieceType.King,
        _ => PieceType.None
    };

    private static char PieceToChar(PieceType piece, Color color)
    {
        char c = piece switch
        {
            PieceType.Pawn => 'P',
            PieceType.Knight => 'N',
            PieceType.Bishop => 'B',
            PieceType.Rook => 'R',
            PieceType.Queen => 'Q',
            PieceType.King => 'K',
            _ => ' '
        };
        return color == Color.White ? c : char.ToLower(c);
    }

    private static int SquareFromString(string square)
    {
        int file = square[0] - 'a';
        int rank = square[1] - '1';
        return Bitboards.MakeSquare(file, rank);
    }

    private static string SquareToString(int square)
    {
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);
        return $"{(char)('a' + file)}{(char)('1' + rank)}";
    }
}
