using System.Runtime.CompilerServices;

namespace Monsta.Engine;

/// <summary>
/// Magic bitboards for efficient sliding piece attack generation
/// </summary>
public static class MagicBitboards
{
    private static readonly ulong[] RookMagics = new ulong[64];
    private static readonly ulong[] BishopMagics = new ulong[64];
    private static readonly ulong[] RookMasks = new ulong[64];
    private static readonly ulong[] BishopMasks = new ulong[64];
    private static readonly int[] RookShifts = new int[64];
    private static readonly int[] BishopShifts = new int[64];
    private static readonly ulong[][] RookAttacks = new ulong[64][];
    private static readonly ulong[][] BishopAttacks = new ulong[64][];

    static MagicBitboards()
    {
        InitializeMagics();
    }

    private static void InitializeMagics()
    {
        // Initialize rook magics and attacks
        for (int square = 0; square < 64; square++)
        {
            RookMasks[square] = GenerateRookMask(square);
            RookShifts[square] = 64 - Bitboards.PopCount(RookMasks[square]);
            RookMagics[square] = FindRookMagic(square);
            
            int attackCount = 1 << Bitboards.PopCount(RookMasks[square]);
            RookAttacks[square] = new ulong[attackCount];
            
            for (int i = 0; i < attackCount; i++)
            {
                ulong occupancy = IndexToOccupancy(i, RookMasks[square]);
                int magicIndex = (int)((occupancy * RookMagics[square]) >> RookShifts[square]);
                RookAttacks[square][magicIndex] = GenerateRookAttacks(square, occupancy);
            }
        }

        // Initialize bishop magics and attacks
        for (int square = 0; square < 64; square++)
        {
            BishopMasks[square] = GenerateBishopMask(square);
            BishopShifts[square] = 64 - Bitboards.PopCount(BishopMasks[square]);
            BishopMagics[square] = FindBishopMagic(square);
            
            int attackCount = 1 << Bitboards.PopCount(BishopMasks[square]);
            BishopAttacks[square] = new ulong[attackCount];
            
            for (int i = 0; i < attackCount; i++)
            {
                ulong occupancy = IndexToOccupancy(i, BishopMasks[square]);
                int magicIndex = (int)((occupancy * BishopMagics[square]) >> BishopShifts[square]);
                BishopAttacks[square][magicIndex] = GenerateBishopAttacks(square, occupancy);
            }
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong GetRookAttacks(int square, ulong occupancy)
    {
        occupancy &= RookMasks[square];
        int index = (int)((occupancy * RookMagics[square]) >> RookShifts[square]);
        return RookAttacks[square][index];
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong GetBishopAttacks(int square, ulong occupancy)
    {
        occupancy &= BishopMasks[square];
        int index = (int)((occupancy * BishopMagics[square]) >> BishopShifts[square]);
        return BishopAttacks[square][index];
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static ulong GetQueenAttacks(int square, ulong occupancy)
    {
        return GetRookAttacks(square, occupancy) | GetBishopAttacks(square, occupancy);
    }

    private static ulong GenerateRookMask(int square)
    {
        ulong mask = 0;
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);

        // Horizontal
        for (int f = file + 1; f < 7; f++)
            mask |= 1UL << Bitboards.MakeSquare(f, rank);
        for (int f = file - 1; f > 0; f--)
            mask |= 1UL << Bitboards.MakeSquare(f, rank);

        // Vertical
        for (int r = rank + 1; r < 7; r++)
            mask |= 1UL << Bitboards.MakeSquare(file, r);
        for (int r = rank - 1; r > 0; r--)
            mask |= 1UL << Bitboards.MakeSquare(file, r);

        return mask;
    }

    private static ulong GenerateBishopMask(int square)
    {
        ulong mask = 0;
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);

        // Diagonal up-right
        for (int f = file + 1, r = rank + 1; f < 7 && r < 7; f++, r++)
            mask |= 1UL << Bitboards.MakeSquare(f, r);

        // Diagonal up-left
        for (int f = file - 1, r = rank + 1; f > 0 && r < 7; f--, r++)
            mask |= 1UL << Bitboards.MakeSquare(f, r);

        // Diagonal down-right
        for (int f = file + 1, r = rank - 1; f < 7 && r > 0; f++, r--)
            mask |= 1UL << Bitboards.MakeSquare(f, r);

        // Diagonal down-left
        for (int f = file - 1, r = rank - 1; f > 0 && r > 0; f--, r--)
            mask |= 1UL << Bitboards.MakeSquare(f, r);

        return mask;
    }

    private static ulong GenerateRookAttacks(int square, ulong occupancy)
    {
        ulong attacks = 0;
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);

        // Horizontal right
        for (int f = file + 1; f < 8; f++)
        {
            int targetSquare = Bitboards.MakeSquare(f, rank);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Horizontal left
        for (int f = file - 1; f >= 0; f--)
        {
            int targetSquare = Bitboards.MakeSquare(f, rank);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Vertical up
        for (int r = rank + 1; r < 8; r++)
        {
            int targetSquare = Bitboards.MakeSquare(file, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Vertical down
        for (int r = rank - 1; r >= 0; r--)
        {
            int targetSquare = Bitboards.MakeSquare(file, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        return attacks;
    }

    private static ulong GenerateBishopAttacks(int square, ulong occupancy)
    {
        ulong attacks = 0;
        int file = Bitboards.GetFile(square);
        int rank = Bitboards.GetRank(square);

        // Diagonal up-right
        for (int f = file + 1, r = rank + 1; f < 8 && r < 8; f++, r++)
        {
            int targetSquare = Bitboards.MakeSquare(f, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Diagonal up-left
        for (int f = file - 1, r = rank + 1; f >= 0 && r < 8; f--, r++)
        {
            int targetSquare = Bitboards.MakeSquare(f, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Diagonal down-right
        for (int f = file + 1, r = rank - 1; f < 8 && r >= 0; f++, r--)
        {
            int targetSquare = Bitboards.MakeSquare(f, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        // Diagonal down-left
        for (int f = file - 1, r = rank - 1; f >= 0 && r >= 0; f--, r--)
        {
            int targetSquare = Bitboards.MakeSquare(f, r);
            attacks |= 1UL << targetSquare;
            if ((occupancy & (1UL << targetSquare)) != 0) break;
        }

        return attacks;
    }

    private static ulong IndexToOccupancy(int index, ulong mask)
    {
        ulong occupancy = 0;
        ulong maskCopy = mask;
        
        for (int i = 0; maskCopy != 0; i++)
        {
            int square = Bitboards.PopLsb(ref maskCopy);
            if ((index & (1 << i)) != 0)
                occupancy |= 1UL << square;
        }
        
        return occupancy;
    }

    // Simplified magic number finding - in a real engine, these would be pre-computed
    private static ulong FindRookMagic(int square)
    {
        // These are known good magic numbers for rooks
        ulong[] rookMagics = {
            0x0080001020400080UL, 0x0040001000200040UL, 0x0080081000200080UL, 0x0080040800100080UL,
            0x0080020400080080UL, 0x0080010200040080UL, 0x0080008001000200UL, 0x0080002040800100UL,
            0x0000800020400080UL, 0x0000400020005000UL, 0x0000801000200080UL, 0x0000800800100080UL,
            0x0000800400080080UL, 0x0000800200040080UL, 0x0000800100020080UL, 0x0000800040800100UL,
            0x0000208000400080UL, 0x0000404000201000UL, 0x0000808010002000UL, 0x0000808008001000UL,
            0x0000808004000800UL, 0x0000808002000400UL, 0x0000010100020004UL, 0x0000020000408104UL,
            0x0000208080004000UL, 0x0000200040005000UL, 0x0000100080200080UL, 0x0000080080100080UL,
            0x0000040080080080UL, 0x0000020080040080UL, 0x0000010080800200UL, 0x0000800080004100UL,
            0x0000204000800080UL, 0x0000200040401000UL, 0x0000100080802000UL, 0x0000080080801000UL,
            0x0000040080800800UL, 0x0000020080800400UL, 0x0000020001010004UL, 0x0000800040800100UL,
            0x0000204000808000UL, 0x0000200040008080UL, 0x0000100020008080UL, 0x0000080010008080UL,
            0x0000040008008080UL, 0x0000020004008080UL, 0x0000010002008080UL, 0x0000004081020004UL,
            0x0000204000800080UL, 0x0000200040008080UL, 0x0000100020008080UL, 0x0000080010008080UL,
            0x0000040008008080UL, 0x0000020004008080UL, 0x0000800100020080UL, 0x0000800041000080UL,
            0x00FFFCDDFCED714AUL, 0x007FFCDDFCED714AUL, 0x003FFFCDFFD88096UL, 0x0000040810002101UL,
            0x0001000204080011UL, 0x0001000204000801UL, 0x0001000082000401UL, 0x0001FFFAABFAD1A2UL
        };
        return rookMagics[square];
    }

    private static ulong FindBishopMagic(int square)
    {
        // These are known good magic numbers for bishops
        ulong[] bishopMagics = {
            0x0002020202020200UL, 0x0002020202020000UL, 0x0004010202000000UL, 0x0004040080000000UL,
            0x0001104000000000UL, 0x0000821040000000UL, 0x0000410410400000UL, 0x0000104104104000UL,
            0x0000040404040400UL, 0x0000020202020200UL, 0x0000040102020000UL, 0x0000040400800000UL,
            0x0000011040000000UL, 0x0000008210400000UL, 0x0000004104104000UL, 0x0000002082082000UL,
            0x0004000808080800UL, 0x0002000404040400UL, 0x0001000202020200UL, 0x0000800802004000UL,
            0x0000800400A00000UL, 0x0000200100884000UL, 0x0000400082082000UL, 0x0000200041041000UL,
            0x0002080010101000UL, 0x0001040008080800UL, 0x0000208004010400UL, 0x0000404004010200UL,
            0x0000840000802000UL, 0x0000404002011000UL, 0x0000808001041000UL, 0x0000404000820800UL,
            0x0001041000202000UL, 0x0000820800101000UL, 0x0000104400080800UL, 0x0000020080080080UL,
            0x0000404040040100UL, 0x0000808100020100UL, 0x0001010100020800UL, 0x0000808080010400UL,
            0x0000820820004000UL, 0x0000410410002000UL, 0x0000082088001000UL, 0x0000002011000800UL,
            0x0000080100400400UL, 0x0001010101000200UL, 0x0002020202000400UL, 0x0001010101000200UL,
            0x0000410410400000UL, 0x0000208208200000UL, 0x0000002084100000UL, 0x0000000020880000UL,
            0x0000001002020000UL, 0x0000040408020000UL, 0x0004040404040000UL, 0x0002020202020000UL,
            0x0000104104104000UL, 0x0000002082082000UL, 0x0000000020841000UL, 0x0000000000208800UL,
            0x0000000010020200UL, 0x0000000404080200UL, 0x0000040404040400UL, 0x0002020202020200UL
        };
        return bishopMagics[square];
    }
}
