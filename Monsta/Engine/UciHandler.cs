using System.Globalization;

namespace Monsta.Engine;

/// <summary>
/// UCI (Universal Chess Interface) protocol handler
/// </summary>
public class UciHandler
{
    private readonly Board _board;
    private readonly Search _search;
    private bool _isRunning;
    private bool _isSearching;
    private Thread? _searchThread;

    // UCI options
    private int _hashSize = 64; // MB
    private bool _ownBook = false;
    private bool _ponder = false;

    public UciHandler()
    {
        _board = new Board();
        _search = new Search(_hashSize);
        _isRunning = true;
    }

    public void Run()
    {
        Console.WriteLine("Monsta Chess Engine v1.0 by Augment Agent");
        
        while (_isRunning)
        {
            try
            {
                string? input = Console.ReadLine();
                if (string.IsNullOrEmpty(input))
                    continue;

                ProcessCommand(input.Trim());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"info string Error: {ex.Message}");
            }
        }
    }

    private void ProcessCommand(string command)
    {
        var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length == 0) return;

        switch (parts[0].ToLower())
        {
            case "uci":
                HandleUci();
                break;
            
            case "debug":
                HandleDebug(parts);
                break;
            
            case "isready":
                HandleIsReady();
                break;
            
            case "setoption":
                HandleSetOption(parts);
                break;
            
            case "register":
                HandleRegister(parts);
                break;
            
            case "ucinewgame":
                HandleNewGame();
                break;
            
            case "position":
                HandlePosition(command);
                break;
            
            case "go":
                HandleGo(parts);
                break;
            
            case "stop":
                HandleStop();
                break;
            
            case "ponderhit":
                HandlePonderHit();
                break;
            
            case "quit":
                HandleQuit();
                break;
            
            default:
                Console.WriteLine($"info string Unknown command: {parts[0]}");
                break;
        }
    }

    private void HandleUci()
    {
        Console.WriteLine("id name Monsta 1.0");
        Console.WriteLine("id author Augment Agent");
        
        // Send options
        Console.WriteLine($"option name Hash type spin default {_hashSize} min 1 max 1024");
        Console.WriteLine($"option name OwnBook type check default {_ownBook.ToString().ToLower()}");
        Console.WriteLine($"option name Ponder type check default {_ponder.ToString().ToLower()}");
        Console.WriteLine("option name UCI_Chess960 type check default false");
        Console.WriteLine("option name UCI_AnalyseMode type check default false");
        Console.WriteLine("option name UCI_LimitStrength type check default false");
        Console.WriteLine("option name UCI_Elo type spin default 2800 min 1000 max 3000");
        
        Console.WriteLine("uciok");
    }

    private void HandleDebug(string[] parts)
    {
        if (parts.Length > 1)
        {
            bool debugMode = parts[1].ToLower() == "on";
            Console.WriteLine($"info string Debug mode: {debugMode}");
        }
    }

    private void HandleIsReady()
    {
        Console.WriteLine("readyok");
    }

    private void HandleSetOption(string[] parts)
    {
        if (parts.Length < 4 || parts[1].ToLower() != "name")
            return;

        string optionName = parts[2].ToLower();
        
        if (parts.Length >= 5 && parts[3].ToLower() == "value")
        {
            string value = string.Join(" ", parts.Skip(4));
            
            switch (optionName)
            {
                case "hash":
                    if (int.TryParse(value, out int hashSize))
                    {
                        _hashSize = Math.Max(1, Math.Min(1024, hashSize));
                        Console.WriteLine($"info string Hash size set to {_hashSize} MB");
                    }
                    break;
                
                case "ownbook":
                    _ownBook = value.ToLower() == "true";
                    Console.WriteLine($"info string Own book: {_ownBook}");
                    break;
                
                case "ponder":
                    _ponder = value.ToLower() == "true";
                    Console.WriteLine($"info string Ponder: {_ponder}");
                    break;
                
                default:
                    Console.WriteLine($"info string Unknown option: {optionName}");
                    break;
            }
        }
    }

    private void HandleRegister(string[] parts)
    {
        // Engine doesn't require registration
        Console.WriteLine("info string Engine does not require registration");
    }

    private void HandleNewGame()
    {
        _board.SetStartingPosition();
        Console.WriteLine("info string New game started");
    }

    private void HandlePosition(string command)
    {
        var parts = command.Split(' ');
        int index = 1;
        
        if (index >= parts.Length) return;
        
        if (parts[index] == "startpos")
        {
            _board.SetStartingPosition();
            index++;
        }
        else if (parts[index] == "fen")
        {
            index++;
            var fenParts = new List<string>();
            
            // Collect FEN parts until we hit "moves" or end
            while (index < parts.Length && parts[index] != "moves")
            {
                fenParts.Add(parts[index]);
                index++;
            }
            
            if (fenParts.Count >= 6)
            {
                string fen = string.Join(" ", fenParts);
                _board.SetFromFen(fen);
            }
        }
        
        // Apply moves if present
        if (index < parts.Length && parts[index] == "moves")
        {
            index++;
            while (index < parts.Length)
            {
                string moveStr = parts[index];
                try
                {
                    var move = Move.FromUciString(moveStr);
                    // TODO: Apply move to board
                    // For now, we'll need to implement proper move application
                }
                catch
                {
                    Console.WriteLine($"info string Invalid move: {moveStr}");
                }
                index++;
            }
        }
    }

    private void HandleGo(string[] parts)
    {
        if (_isSearching)
        {
            Console.WriteLine("info string Already searching");
            return;
        }

        var searchParams = ParseGoCommand(parts);
        
        _isSearching = true;
        _searchThread = new Thread(() =>
        {
            try
            {
                var result = _search.StartSearch(_board, searchParams.Depth, searchParams.TimeMs, searchParams.Nodes);
                
                if (!result.BestMove.IsNull)
                {
                    Console.WriteLine($"bestmove {result.BestMove.ToUciString()}");
                }
                else
                {
                    Console.WriteLine("bestmove 0000");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"info string Search error: {ex.Message}");
                Console.WriteLine("bestmove 0000");
            }
            finally
            {
                _isSearching = false;
            }
        });
        
        _searchThread.Start();
    }

    private void HandleStop()
    {
        if (_isSearching)
        {
            _search.Stop();
            _searchThread?.Join(1000); // Wait up to 1 second
            _isSearching = false;
        }
    }

    private void HandlePonderHit()
    {
        // Convert pondering to normal search
        Console.WriteLine("info string Ponder hit");
    }

    private void HandleQuit()
    {
        if (_isSearching)
        {
            _search.Stop();
            _searchThread?.Join(1000);
        }
        _isRunning = false;
    }

    private SearchParameters ParseGoCommand(string[] parts)
    {
        var searchParams = new SearchParameters();
        
        for (int i = 1; i < parts.Length; i++)
        {
            switch (parts[i].ToLower())
            {
                case "depth":
                    if (i + 1 < parts.Length && int.TryParse(parts[i + 1], out int depth))
                    {
                        searchParams.Depth = depth;
                        i++;
                    }
                    break;
                
                case "nodes":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long nodes))
                    {
                        searchParams.Nodes = nodes;
                        i++;
                    }
                    break;
                
                case "movetime":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long moveTime))
                    {
                        searchParams.TimeMs = moveTime;
                        i++;
                    }
                    break;
                
                case "wtime":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long wtime))
                    {
                        if (_board.SideToMove == Color.White)
                            searchParams.TimeMs = CalculateTimeForMove(wtime, searchParams.WInc);
                        i++;
                    }
                    break;
                
                case "btime":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long btime))
                    {
                        if (_board.SideToMove == Color.Black)
                            searchParams.TimeMs = CalculateTimeForMove(btime, searchParams.BInc);
                        i++;
                    }
                    break;
                
                case "winc":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long winc))
                    {
                        searchParams.WInc = winc;
                        i++;
                    }
                    break;
                
                case "binc":
                    if (i + 1 < parts.Length && long.TryParse(parts[i + 1], out long binc))
                    {
                        searchParams.BInc = binc;
                        i++;
                    }
                    break;
                
                case "infinite":
                    searchParams.Infinite = true;
                    break;
                
                case "ponder":
                    searchParams.Ponder = true;
                    break;
            }
        }
        
        return searchParams;
    }

    private long CalculateTimeForMove(long timeLeft, long increment)
    {
        // Simple time management: use 1/30th of remaining time plus increment
        return Math.Max(100, timeLeft / 30 + increment * 3 / 4);
    }
}

public struct SearchParameters
{
    public int Depth;
    public long TimeMs;
    public long Nodes;
    public long WInc;
    public long BInc;
    public bool Infinite;
    public bool Ponder;
}
