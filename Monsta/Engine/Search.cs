using System.Diagnostics;

namespace Monsta.Engine;

/// <summary>
/// Main search engine with negamax, alpha-beta pruning, and advanced techniques
/// </summary>
public class Search
{
    private readonly TranspositionTable _transpositionTable;
    private readonly Stopwatch _stopwatch;
    private bool _stopSearch;
    private int _maxDepth;
    private long _timeLimit;
    private long _nodesSearched;
    private Move _bestMove;
    private int _bestScore;

    // Search statistics
    public long NodesSearched => _nodesSearched;
    public Move BestMove => _bestMove;
    public int BestScore => _bestScore;

    // Constants
    private const int Infinity = 30000;
    private const int MateValue = 20000;
    private const int MaxDepth = 64;

    public Search(int hashSizeMB = 64)
    {
        _transpositionTable = new TranspositionTable(hashSizeMB);
        _stopwatch = new Stopwatch();
    }

    public SearchResult StartSearch(Board board, int depth = 0, long timeMs = 0, long nodes = 0)
    {
        _stopSearch = false;
        _maxDepth = depth > 0 ? Math.Min(depth, MaxDepth) : MaxDepth;
        _timeLimit = timeMs;
        _nodesSearched = 0;
        _bestMove = Move.NullMove;
        _bestScore = -Infinity;

        _stopwatch.Restart();

        try
        {
            // Iterative deepening
            for (int currentDepth = 1; currentDepth <= _maxDepth; currentDepth++)
            {
                if (_stopSearch) break;

                int score = NegamaxRoot(board, currentDepth, -Infinity, Infinity);
                
                if (_stopSearch) break;

                _bestScore = score;
                
                // Send info to UCI
                long elapsed = _stopwatch.ElapsedMilliseconds;
                long nps = elapsed > 0 ? (_nodesSearched * 1000) / elapsed : 0;
                
                Console.WriteLine($"info depth {currentDepth} score cp {score} time {elapsed} nodes {_nodesSearched} nps {nps} pv {_bestMove}");

                // Check time limit
                if (_timeLimit > 0 && elapsed >= _timeLimit)
                    break;

                // Check node limit
                if (nodes > 0 && _nodesSearched >= nodes)
                    break;

                // If we found a mate, no need to search deeper
                if (Math.Abs(score) > MateValue - 100)
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"info string Search error: {ex.Message}");
        }

        _stopwatch.Stop();

        return new SearchResult
        {
            BestMove = _bestMove,
            Score = _bestScore,
            Depth = _maxDepth,
            NodesSearched = _nodesSearched,
            TimeMs = _stopwatch.ElapsedMilliseconds
        };
    }

    public void Stop()
    {
        _stopSearch = true;
    }

    private int NegamaxRoot(Board board, int depth, int alpha, int beta)
    {
        var moves = MoveGenerator.GenerateMoves(board);
        if (moves.Count == 0)
        {
            return board.IsInCheck(board.SideToMove) ? -MateValue : 0; // Checkmate or stalemate
        }

        // Order moves for better pruning
        OrderMoves(board, moves, Move.NullMove);

        int bestScore = -Infinity;
        Move bestMove = moves[0];

        foreach (var move in moves)
        {
            if (_stopSearch) break;

            var gameState = MakeMove(board, move);
            int score = -Negamax(board, depth - 1, -beta, -alpha, 1);
            UnmakeMove(board, move, gameState);

            if (score > bestScore)
            {
                bestScore = score;
                bestMove = move;
                
                if (score > alpha)
                {
                    alpha = score;
                }
            }
        }

        _bestMove = bestMove;
        return bestScore;
    }

    private int Negamax(Board board, int depth, int alpha, int beta, int ply)
    {
        if (_stopSearch) return 0;

        _nodesSearched++;

        // Check time limit periodically
        if ((_nodesSearched & 2047) == 0 && _timeLimit > 0 && _stopwatch.ElapsedMilliseconds >= _timeLimit)
        {
            _stopSearch = true;
            return 0;
        }

        ulong hash = board.Hash;
        
        // Transposition table lookup
        if (_transpositionTable.Probe(hash, depth, alpha, beta, ply, out int ttScore, out Move ttMove))
        {
            return ttScore;
        }

        // Terminal node check
        if (depth <= 0)
        {
            return Quiescence(board, alpha, beta, ply);
        }

        var moves = MoveGenerator.GenerateMoves(board);
        if (moves.Count == 0)
        {
            return board.IsInCheck(board.SideToMove) ? -MateValue + ply : 0;
        }

        // Null move pruning
        if (depth >= 3 && !board.IsInCheck(board.SideToMove) && HasNonPawnMaterial(board))
        {
            var nullGameState = MakeNullMove(board);
            int nullScore = -Negamax(board, depth - 3, -beta, -beta + 1, ply + 1);
            UnmakeNullMove(board, nullGameState);

            if (nullScore >= beta)
            {
                return beta; // Null move cutoff
            }
        }

        // Order moves
        OrderMoves(board, moves, ttMove);

        int bestScore = -Infinity;
        Move bestMove = Move.NullMove;
        NodeType nodeType = NodeType.UpperBound;
        int movesSearched = 0;

        foreach (var move in moves)
        {
            if (_stopSearch) break;

            var gameState = MakeMove(board, move);
            int score;

            // Principal Variation Search (PVS)
            if (movesSearched == 0)
            {
                score = -Negamax(board, depth - 1, -beta, -alpha, ply + 1);
            }
            else
            {
                // Late Move Reductions (LMR)
                int reduction = 0;
                if (depth >= 3 && movesSearched >= 4 && !move.IsCapture && !move.IsPromotion && !board.IsInCheck(board.SideToMove))
                {
                    reduction = 1;
                    if (movesSearched >= 8) reduction = 2;
                }

                score = -Negamax(board, depth - 1 - reduction, -alpha - 1, -alpha, ply + 1);
                
                if (score > alpha && reduction > 0)
                {
                    score = -Negamax(board, depth - 1, -alpha - 1, -alpha, ply + 1);
                }
                
                if (score > alpha && score < beta)
                {
                    score = -Negamax(board, depth - 1, -beta, -alpha, ply + 1);
                }
            }

            UnmakeMove(board, move, gameState);
            movesSearched++;

            if (score > bestScore)
            {
                bestScore = score;
                bestMove = move;

                if (score > alpha)
                {
                    alpha = score;
                    nodeType = NodeType.Exact;

                    if (score >= beta)
                    {
                        nodeType = NodeType.LowerBound;
                        break; // Beta cutoff
                    }
                }
            }
        }

        // Store in transposition table
        _transpositionTable.Store(hash, depth, bestScore, nodeType, bestMove, ply);

        return bestScore;
    }

    private int Quiescence(Board board, int alpha, int beta, int ply)
    {
        _nodesSearched++;

        int standPat = Evaluation.Evaluate(board);
        
        if (standPat >= beta)
            return beta;
        
        if (standPat > alpha)
            alpha = standPat;

        var captures = MoveGenerator.GenerateMoves(board, capturesOnly: true);
        OrderMoves(board, captures, Move.NullMove);

        foreach (var capture in captures)
        {
            if (_stopSearch) break;

            // Delta pruning - skip captures that can't improve position
            int captureValue = GetPieceValue(capture.Captured);
            if (standPat + captureValue + 200 < alpha)
                continue;

            var gameState = MakeMove(board, capture);
            int score = -Quiescence(board, -beta, -alpha, ply + 1);
            UnmakeMove(board, capture, gameState);

            if (score >= beta)
                return beta;
            
            if (score > alpha)
                alpha = score;
        }

        return alpha;
    }

    private void OrderMoves(Board board, List<Move> moves, Move hashMove)
    {
        // Simple move ordering: hash move, captures, then other moves
        moves.Sort((a, b) =>
        {
            if (a == hashMove) return -1;
            if (b == hashMove) return 1;
            
            if (a.IsCapture && !b.IsCapture) return -1;
            if (b.IsCapture && !a.IsCapture) return 1;
            
            if (a.IsCapture && b.IsCapture)
            {
                int aValue = GetPieceValue(a.Captured) - GetPieceValue(GetMovingPiece(board, a));
                int bValue = GetPieceValue(b.Captured) - GetPieceValue(GetMovingPiece(board, b));
                return bValue.CompareTo(aValue); // MVV-LVA
            }
            
            if (a.IsPromotion && !b.IsPromotion) return -1;
            if (b.IsPromotion && !a.IsPromotion) return 1;
            
            return 0;
        });
    }

    private PieceType GetMovingPiece(Board board, Move move)
    {
        var (piece, _) = board.GetPiece(move.From);
        return piece;
    }

    private int GetPieceValue(PieceType piece) => piece switch
    {
        PieceType.Pawn => 100,
        PieceType.Knight => 320,
        PieceType.Bishop => 330,
        PieceType.Rook => 500,
        PieceType.Queen => 900,
        PieceType.King => 20000,
        _ => 0
    };

    private bool HasNonPawnMaterial(Board board)
    {
        Color sideToMove = board.SideToMove;
        return board.GetPieceBitboard(PieceType.Knight, sideToMove) != 0 ||
               board.GetPieceBitboard(PieceType.Bishop, sideToMove) != 0 ||
               board.GetPieceBitboard(PieceType.Rook, sideToMove) != 0 ||
               board.GetPieceBitboard(PieceType.Queen, sideToMove) != 0;
    }

    // Simplified move making/unmaking for search
    private GameState MakeMove(Board board, Move move)
    {
        // Store current state
        var state = new GameState
        {
            CastlingRights = board.CastlingRights,
            EnPassantSquare = board.EnPassantSquare,
            HalfMoveClock = board.HalfMoveClock,
            Hash = board.Hash
        };

        // This is a simplified implementation
        // In a real engine, this would be much more efficient
        var fen = board.ToFen();
        var moves = new List<Move> { move };
        
        // Apply move (simplified)
        // TODO: Implement efficient make/unmake move
        
        return state;
    }

    private void UnmakeMove(Board board, Move move, GameState state)
    {
        // Restore state (simplified)
        // TODO: Implement efficient unmake move
    }

    private GameState MakeNullMove(Board board)
    {
        var state = new GameState
        {
            CastlingRights = board.CastlingRights,
            EnPassantSquare = board.EnPassantSquare,
            HalfMoveClock = board.HalfMoveClock,
            Hash = board.Hash
        };

        // Switch side to move
        // TODO: Implement null move

        return state;
    }

    private void UnmakeNullMove(Board board, GameState state)
    {
        // Restore state
        // TODO: Implement null move unmake
    }
}

public struct GameState
{
    public int CastlingRights;
    public int EnPassantSquare;
    public int HalfMoveClock;
    public ulong Hash;
}

public struct SearchResult
{
    public Move BestMove;
    public int Score;
    public int Depth;
    public long NodesSearched;
    public long TimeMs;
}
